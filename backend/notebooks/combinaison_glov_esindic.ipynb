import pandas as pd
import ast

def load_csv_direct(csv_file):
    try:
        df_raw = pd.read_csv(csv_file)
        print(f"Fichier charge: {csv_file}")
        print(f"Nombre de lignes: {len(df_raw)}")
        return df_raw
    except Exception as e:
        print(f"Erreur: {str(e)}")
        return None

def parse_json_columns(df, json_columns):
    df_copy = df.copy()
    for col in json_columns:
        if col in df_copy.columns:
            df_copy[col] = df_copy[col].apply(lambda x: ast.literal_eval(x) if pd.notna(x) and x != '[]' and x != '{}' else [])
    return df_copy

def explode_column(df, col_name, parent_col='Parent'):
    rows = []
    for _, row in df.iterrows():
        data_list = row.get(col_name)
        if isinstance(data_list, list) and len(data_list) > 0:
            for item in data_list:
                if isinstance(item, dict):
                    item_copy = item.copy()
                    item_copy[parent_col] = row[parent_col]
                    rows.append(item_copy)
    return pd.DataFrame(rows)

# Chargement Gestion Locative
print("=== CHARGEMENT GESTION LOCATIVE ===")
df_biens_raw = load_csv_direct('G_locative.csv')

if df_biens_raw is not None:
    json_columns = ['proprietaires', 'locataires', 'charges', 'type', 'actifs']
    df_biens = parse_json_columns(df_biens_raw, json_columns)
    
    df_biens.rename(columns={
        'libelle': 'Bien',
        'adresse': 'Adresse',
        'proprietaires': 'Proprietaires',
        'locataires': 'Locataires',
        'charges': 'Charges',
        'totalCharges': 'TotalCharges',
        'totaLoyer': 'TotalLoyer',
        'totalImpayer': 'TotalImpayer',
        'type': 'Type_Dict'
    }, inplace=True)
    
    df_biens['Type'] = df_biens['Type_Dict'].apply(lambda x: x.get('libelle', 'N/A') if isinstance(x, dict) else 'N/A')
    
    df_proprietaires_locative = explode_column(df_biens, 'Proprietaires', 'Bien')
    df_locataires_locative = explode_column(df_biens, 'Locataires', 'Bien')
    
    print(f"Biens charges: {len(df_biens)}")
    print(f"Proprietaires: {len(df_proprietaires_locative)}")
    print(f"Locataires: {len(df_locataires_locative)}")
else:
    df_biens = pd.DataFrame()
    df_proprietaires_locative = pd.DataFrame()
    df_locataires_locative = pd.DataFrame()

# Chargement E-Syndic
print("\n=== CHARGEMENT E-SYNDIC ===")
df_esyndic_raw = load_csv_direct('E_syndic.csv')

if df_esyndic_raw is not None:
    parsed_esyndic = ast.literal_eval(df_esyndic_raw['data'].iloc[0])
    df_programmes = pd.DataFrame(parsed_esyndic)
    
    df_programmes.rename(columns={
        'libelle': 'Programme',
        'pays': 'Pays',
        'ville': 'Ville',
        'superficie': 'Superficie',
        'proprietaire': 'Proprietaire',
        'locataires': 'Locataires',
        'incidents': 'Incidents',
        'evenements': 'Evenements'
    }, inplace=True)
    
    df_proprietaires_esyndic = explode_column(df_programmes, 'Proprietaire', 'Programme')
    df_locataires_esyndic = explode_column(df_programmes, 'Locataires', 'Programme')
    df_incidents = explode_column(df_programmes, 'Incidents', 'Programme')
    df_evenements = explode_column(df_programmes, 'Evenements', 'Programme')
    
    print(f"Programmes charges: {len(df_programmes)}")
    print(f"Proprietaires: {len(df_proprietaires_esyndic)}")
    print(f"Locataires: {len(df_locataires_esyndic)}")
    print(f"Incidents: {len(df_incidents)}")
    print(f"Evenements: {len(df_evenements)}")
else:
    df_programmes = pd.DataFrame()
    df_proprietaires_esyndic = pd.DataFrame()
    df_locataires_esyndic = pd.DataFrame()
    df_incidents = pd.DataFrame()
    df_evenements = pd.DataFrame()

class CombinedMetrics:
    def __init__(self, df_biens, df_programmes, df_prop_loc, df_loc_loc, df_prop_syn, df_loc_syn, df_incidents, df_evenements):
        self.df_biens = df_biens
        self.df_programmes = df_programmes
        self.df_proprietaires_locative = df_prop_loc
        self.df_locataires_locative = df_loc_loc
        self.df_proprietaires_esyndic = df_prop_syn
        self.df_locataires_esyndic = df_loc_syn
        self.df_incidents = df_incidents
        self.df_evenements = df_evenements
    
    def get_fusion_dataframes_programme_locataires(self):
        """Fusion des dataframes programme et locataires avec type de bien par programme"""
        results = []
        
        if not self.df_programmes.empty:
            for _, prog_row in self.df_programmes.iterrows():
                programme_nom = prog_row['Programme']
                ville = prog_row.get('Ville', 'N/A')
                pays = prog_row.get('Pays', 'N/A')
                
                nb_proprietaires = len(self.df_proprietaires_esyndic[self.df_proprietaires_esyndic['Programme'] == programme_nom])
                nb_locataires = len(self.df_locataires_esyndic[self.df_locataires_esyndic['Programme'] == programme_nom])
                
                results.append({
                    'Programme': programme_nom,
                    'Ville': ville,
                    'Pays': pays,
                    'Type_Bien': 'Programme_Immobilier',
                    'Nb_Proprietaires': nb_proprietaires,
                    'Nb_Locataires': nb_locataires,
                    'Total_Personnes': nb_proprietaires + nb_locataires
                })
        
        return pd.DataFrame(results)
    
    def get_fusion_proprietaires_biens_locataires(self):
        """Fusion pour afficher pour chaque proprietaire le nombre de biens en location et noms des locataires"""
        results = []
        
        # Gestion Locative
        if not self.df_proprietaires_locative.empty:
            proprietaires_uniques = self.df_proprietaires_locative.groupby(['name', 'prenoms']).size().reset_index(name='count')
            
            for _, prop in proprietaires_uniques.iterrows():
                proprietaire_nom = f"{prop['name']} {prop['prenoms']}".strip()
                
                biens_proprietaire = self.df_proprietaires_locative[
                    (self.df_proprietaires_locative['name'] == prop['name']) & 
                    (self.df_proprietaires_locative['prenoms'] == prop['prenoms'])
                ]
                
                nb_biens_total = len(biens_proprietaire)
                nb_biens_loues = 0
                noms_locataires = []
                
                for _, bien_prop in biens_proprietaire.iterrows():
                    bien_nom = bien_prop['Bien']
                    
                    locataires_bien = self.df_locataires_locative[self.df_locataires_locative['Bien'] == bien_nom]
                    if not locataires_bien.empty:
                        nb_biens_loues += 1
                        for _, loc in locataires_bien.iterrows():
                            nom_locataire = f"{loc.get('name', '')} {loc.get('prenoms', '')}".strip()
                            if nom_locataire:
                                noms_locataires.append(f"{nom_locataire} ({bien_nom})")
                
                results.append({
                    'Source': 'Gestion_Locative',
                    'Proprietaire': proprietaire_nom,
                    'Nb_Biens_Total': nb_biens_total,
                    'Nb_Biens_Loues': nb_biens_loues,
                    'Noms_Locataires_Biens': '; '.join(noms_locataires) if noms_locataires else 'Aucun'
                })
        
        # E-Syndic
        if not self.df_proprietaires_esyndic.empty:
            proprietaires_uniques = self.df_proprietaires_esyndic.groupby(['name', 'prenoms']).size().reset_index(name='count')
            
            for _, prop in proprietaires_uniques.iterrows():
                proprietaire_nom = f"{prop['name']} {prop['prenoms']}".strip()
                
                programmes_proprietaire = self.df_proprietaires_esyndic[
                    (self.df_proprietaires_esyndic['name'] == prop['name']) & 
                    (self.df_proprietaires_esyndic['prenoms'] == prop['prenoms'])
                ]
                
                nb_programmes_total = len(programmes_proprietaire)
                nb_programmes_avec_locataires = 0
                noms_locataires = []
                
                for _, prog_prop in programmes_proprietaire.iterrows():
                    programme_nom = prog_prop['Programme']
                    
                    locataires_prog = self.df_locataires_esyndic[self.df_locataires_esyndic['Programme'] == programme_nom]
                    if not locataires_prog.empty:
                        nb_programmes_avec_locataires += 1
                        for _, loc in locataires_prog.iterrows():
                            nom_locataire = f"{loc.get('name', '')} {loc.get('prenoms', '')}".strip()
                            if nom_locataire:
                                noms_locataires.append(f"{nom_locataire} ({programme_nom})")
                
                results.append({
                    'Source': 'E_Syndic',
                    'Proprietaire': proprietaire_nom,
                    'Nb_Biens_Total': nb_programmes_total,
                    'Nb_Biens_Loues': nb_programmes_avec_locataires,
                    'Noms_Locataires_Biens': '; '.join(noms_locataires) if noms_locataires else 'Aucun'
                })
        
        return pd.DataFrame(results)
    
    def get_nombre_evenements_par_programme(self):
        """Dataframe pour afficher le nombre d'evenements par programme"""
        if self.df_programmes.empty:
            return pd.DataFrame(columns=['Programme', 'Ville', 'Nb_Evenements'])
        
        results = []
        for _, row in self.df_programmes.iterrows():
            programme = row['Programme']
            ville = row.get('Ville', 'N/A')
            
            nb_evenements = len(self.df_evenements[self.df_evenements['Programme'] == programme])
            
            results.append({
                'Programme': programme,
                'Ville': ville,
                'Nb_Evenements': nb_evenements
            })
        
        return pd.DataFrame(results)
    
    def get_evenements_par_proprietaires_locataires(self):
        """Dataframe pour afficher le nombre d'evenements organises par proprietaires et locataires"""
        results = []
        
        # Evenements par proprietaires (E-Syndic)
        if not self.df_proprietaires_esyndic.empty and not self.df_evenements.empty:
            proprietaires_uniques = self.df_proprietaires_esyndic.groupby(['name', 'prenoms']).size().reset_index(name='count')
            
            for _, prop in proprietaires_uniques.iterrows():
                proprietaire_nom = f"{prop['name']} {prop['prenoms']}".strip()
                
                programmes_proprietaire = self.df_proprietaires_esyndic[
                    (self.df_proprietaires_esyndic['name'] == prop['name']) & 
                    (self.df_proprietaires_esyndic['prenoms'] == prop['prenoms'])
                ]['Programme'].unique()
                
                nb_evenements = 0
                for programme in programmes_proprietaire:
                    nb_evenements += len(self.df_evenements[self.df_evenements['Programme'] == programme])
                
                results.append({
                    'Type_Personne': 'Proprietaire',
                    'Nom_Personne': proprietaire_nom,
                    'Nb_Evenements': nb_evenements,
                    'Source': 'E_Syndic'
                })
        
        # Evenements par locataires (E-Syndic)
        if not self.df_locataires_esyndic.empty and not self.df_evenements.empty:
            locataires_uniques = self.df_locataires_esyndic.groupby(['name', 'prenoms']).size().reset_index(name='count')
            
            for _, loc in locataires_uniques.iterrows():
                locataire_nom = f"{loc['name']} {loc['prenoms']}".strip()
                
                programmes_locataire = self.df_locataires_esyndic[
                    (self.df_locataires_esyndic['name'] == loc['name']) & 
                    (self.df_locataires_esyndic['prenoms'] == loc['prenoms'])
                ]['Programme'].unique()
                
                nb_evenements = 0
                for programme in programmes_locataire:
                    nb_evenements += len(self.df_evenements[self.df_evenements['Programme'] == programme])
                
                results.append({
                    'Type_Personne': 'Locataire',
                    'Nom_Personne': locataire_nom,
                    'Nb_Evenements': nb_evenements,
                    'Source': 'E_Syndic'
                })
        
        return pd.DataFrame(results)
    
    def get_nombre_incidents_par_programme(self):
        """Dataframe pour afficher le nombre d'incidents par programme"""
        if self.df_programmes.empty:
            return pd.DataFrame(columns=['Programme', 'Ville', 'Nb_Incidents'])
        
        results = []
        for _, row in self.df_programmes.iterrows():
            programme = row['Programme']
            ville = row.get('Ville', 'N/A')
            
            nb_incidents = len(self.df_incidents[self.df_incidents['Programme'] == programme])
            
            results.append({
                'Programme': programme,
                'Ville': ville,
                'Nb_Incidents': nb_incidents
            })
        
        return pd.DataFrame(results)
    
    def generer_rapport_complet(self):
        print("RAPPORT COMBINE E-SYNDIC + GESTION LOCATIVE")
        print("=" * 50)
        
        # 1. Fusion dataframes programme et locataires par programme
        print("\n1. FUSION PROGRAMMES ET LOCATAIRES PAR PROGRAMME")
        df_fusion_prog = self.get_fusion_dataframes_programme_locataires()
        if not df_fusion_prog.empty:
            display(df_fusion_prog)
            print(f"Total proprietaires: {df_fusion_prog['Nb_Proprietaires'].sum()}")
            print(f"Total locataires: {df_fusion_prog['Nb_Locataires'].sum()}")
        else:
            print("Aucune donnee de programmes disponible")
        
        # 2. Fusion proprietaires avec biens et noms des locataires
        print("\n2. PROPRIETAIRES AVEC BIENS ET NOMS DES LOCATAIRES")
        df_fusion_prop = self.get_fusion_proprietaires_biens_locataires()
        if not df_fusion_prop.empty:
            display(df_fusion_prop)
        else:
            print("Aucune donnee de proprietaires disponible")
        
        # 3. Nombre d'evenements par programme
        print("\n3. NOMBRE D'EVENEMENTS PAR PROGRAMME")
        df_events_prog = self.get_nombre_evenements_par_programme()
        if not df_events_prog.empty:
            display(df_events_prog)
            print(f"Total evenements: {df_events_prog['Nb_Evenements'].sum()}")
        else:
            print("Aucune donnee d'evenements disponible")
        
        # 4. Evenements par proprietaires et locataires
        print("\n4. EVENEMENTS PAR PROPRIETAIRES ET LOCATAIRES")
        df_events_personnes = self.get_evenements_par_proprietaires_locataires()
        if not df_events_personnes.empty:
            display(df_events_personnes)
            proprietaires_events = df_events_personnes[df_events_personnes['Type_Personne'] == 'Proprietaire']
            locataires_events = df_events_personnes[df_events_personnes['Type_Personne'] == 'Locataire']
            print(f"Evenements organises par proprietaires: {proprietaires_events['Nb_Evenements'].sum()}")
            print(f"Evenements organises par locataires: {locataires_events['Nb_Evenements'].sum()}")
        else:
            print("Aucune donnee d'evenements par personnes disponible")
        
        # 5. Nombre d'incidents par programme
        print("\n5. NOMBRE D'INCIDENTS PAR PROGRAMME")
        df_incidents_prog = self.get_nombre_incidents_par_programme()
        if not df_incidents_prog.empty:
            display(df_incidents_prog)
            print(f"Total incidents: {df_incidents_prog['Nb_Incidents'].sum()}")
        else:
            print("Aucune donnee d'incidents disponible")
        
        return {
            'fusion_programmes': df_fusion_prog,
            'fusion_proprietaires': df_fusion_prop,
            'evenements_programmes': df_events_prog,
            'evenements_personnes': df_events_personnes,
            'incidents_programmes': df_incidents_prog
        }

# Creer l'instance de la classe
metrics = CombinedMetrics(
    df_biens, df_programmes, 
    df_proprietaires_locative, df_locataires_locative,
    df_proprietaires_esyndic, df_locataires_esyndic,
    df_incidents, df_evenements
)

# Generer le rapport complet
resultats = metrics.generer_rapport_complet()

