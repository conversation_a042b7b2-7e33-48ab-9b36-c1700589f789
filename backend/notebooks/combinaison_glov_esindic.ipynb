{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ANALYSE COMBINEE E-SYNDIC + GESTION LOCATIVE\n", "\n", "## Fonctionnalites implementees:\n", "\n", "1. **Fusion des dataframes programme et locataires** - Affiche pour chaque programme le nombre de proprietaires et locataires avec le type de bien\n", "2. **Fusion proprietaires avec biens et locataires** - Pour chaque proprietaire, affiche le nombre de biens en location et les noms des locataires\n", "3. **Nombre d'evenements par programme** - Dataframe montrant le nombre d'evenements par programme\n", "4. **Evenements par proprietaires et locataires** - Nombre d'evenements organises par proprietaires et par locataires\n", "5. **Nombre d'incidents par programme** - Dataframe montrant le nombre d'incidents par programme"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import ast"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fonctions de chargement"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_csv_direct(csv_file):\n", "    try:\n", "        df_raw = pd.read_csv(csv_file)\n", "        print(f\"Fichier charge: {csv_file}\")\n", "        print(f\"Nombre de lignes: {len(df_raw)}\")\n", "        return df_raw\n", "    except Exception as e:\n", "        print(f\"Erreur: {str(e)}\")\n", "        return None\n", "\n", "def parse_json_columns(df, json_columns):\n", "    df_copy = df.copy()\n", "    for col in json_columns:\n", "        if col in df_copy.columns:\n", "            df_copy[col] = df_copy[col].apply(lambda x: ast.literal_eval(x) if pd.notna(x) and x != '[]' and x != '{}' else [])\n", "    return df_copy\n", "\n", "def explode_column(df, col_name, parent_col='Parent'):\n", "    rows = []\n", "    for _, row in df.iterrows():\n", "        data_list = row.get(col_name)\n", "        if isinstance(data_list, list) and len(data_list) > 0:\n", "            for item in data_list:\n", "                if isinstance(item, dict):\n", "                    item_copy = item.copy()\n", "                    item_copy[parent_col] = row[parent_col]\n", "                    rows.append(item_copy)\n", "    return pd.DataFrame(rows)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chargement des donnees"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Chargement Gestion Locative\n", "print(\"=== CHARGEMENT GESTION LOCATIVE ===\")\n", "df_biens_raw = load_csv_direct('G_locative.csv')\n", "\n", "if df_biens_raw is not None:\n", "    json_columns = ['proprietaires', 'locataires', 'charges', 'type', 'actifs']\n", "    df_biens = parse_json_columns(df_biens_raw, json_columns)\n", "    \n", "    df_biens.rename(columns={\n", "        'libelle': 'Bien',\n", "        'adresse': '<PERSON>ress<PERSON>',\n", "        'proprietaires': 'Proprietaires',\n", "        'locataires': 'Locataires',\n", "        'charges': 'Charges',\n", "        'totalCharges': 'TotalCharges',\n", "        'totaLoyer': 'TotalLoyer',\n", "        'totalImpayer': 'TotalImpayer',\n", "        'type': 'Type_Dict'\n", "    }, inplace=True)\n", "    \n", "    df_biens['Type'] = df_biens['Type_Dict'].apply(lambda x: x.get('libelle', 'N/A') if isinstance(x, dict) else 'N/A')\n", "    \n", "    df_proprietaires_locative = explode_column(df_biens, 'Proprietaires', 'Bien')\n", "    df_locataires_locative = explode_column(df_biens, 'Locataires', 'Bien')\n", "    \n", "    print(f\"Biens charges: {len(df_biens)}\")\n", "    print(f\"Proprietaires: {len(df_proprietaires_locative)}\")\n", "    print(f\"Locataires: {len(df_locataires_locative)}\")\n", "else:\n", "    df_biens = pd.DataFrame()\n", "    df_proprietaires_locative = pd.DataFrame()\n", "    df_locataires_locative = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Chargement E-Syndic\n", "print(\"\\n=== CHARGEMENT E-SYNDIC ===\")\n", "df_esyndic_raw = load_csv_direct('E_syndic.csv')\n", "\n", "if df_esyndic_raw is not None:\n", "    parsed_esyndic = ast.literal_eval(df_esyndic_raw['data'].iloc[0])\n", "    df_programmes = pd.DataFrame(parsed_esyndic)\n", "    \n", "    df_programmes.rename(columns={\n", "        'libelle': 'Programme',\n", "        'pays': 'Pays',\n", "        'ville': 'Ville',\n", "        'superficie': 'Superficie',\n", "        'proprietaire': 'Pro<PERSON>rietaire',\n", "        'locataires': 'Locataires',\n", "        'incidents': 'Incidents',\n", "        'evenements': 'Evenements'\n", "    }, inplace=True)\n", "    \n", "    df_proprietaires_esyndic = explode_column(df_programmes, 'Proprietaire', 'Programme')\n", "    df_locataires_esyndic = explode_column(df_programmes, 'Locataires', 'Programme')\n", "    df_incidents = explode_column(df_programmes, 'Incidents', 'Programme')\n", "    df_evenements = explode_column(df_programmes, 'Evenements', 'Programme')\n", "    \n", "    print(f\"Programmes charges: {len(df_programmes)}\")\n", "    print(f\"Proprietaires: {len(df_proprietaires_esyndic)}\")\n", "    print(f\"Locataires: {len(df_locataires_esyndic)}\")\n", "    print(f\"Incidents: {len(df_incidents)}\")\n", "    print(f\"Evenements: {len(df_evenements)}\")\n", "else:\n", "    df_programmes = pd.DataFrame()\n", "    df_proprietaires_esyndic = pd.DataFrame()\n", "    df_locataires_esyndic = pd.DataFrame()\n", "    df_incidents = pd.DataFrame()\n", "    df_evenements = pd.DataFrame()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Classe pour les metriques"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class CombinedMetrics:\n", "    def __init__(self, df_biens, df_programmes, df_prop_loc, df_loc_loc, df_prop_syn, df_loc_syn, df_incidents, df_evenements):\n", "        self.df_biens = df_biens\n", "        self.df_programmes = df_programmes\n", "        self.df_proprietaires_locative = df_prop_loc\n", "        self.df_locataires_locative = df_loc_loc\n", "        self.df_proprietaires_esyndic = df_prop_syn\n", "        self.df_locataires_esyndic = df_loc_syn\n", "        self.df_incidents = df_incidents\n", "        self.df_evenements = df_evenements\n", "    \n", "    def get_fusion_dataframes_programme_locataires(self):\n", "        \"\"\"Fusion des dataframes programme et locataires avec type de bien par programme\"\"\"\n", "        results = []\n", "        \n", "        if not self.df_programmes.empty:\n", "            for _, prog_row in self.df_programmes.iterrows():\n", "                programme_nom = prog_row['Programme']\n", "                ville = prog_row.get('Ville', 'N/A')\n", "                pays = prog_row.get('Pays', 'N/A')\n", "                \n", "                nb_proprietaires = len(self.df_proprietaires_esyndic[self.df_proprietaires_esyndic['Programme'] == programme_nom])\n", "                nb_locataires = len(self.df_locataires_esyndic[self.df_locataires_esyndic['Programme'] == programme_nom])\n", "                \n", "                results.append({\n", "                    'Programme': programme_nom,\n", "                    'Ville': ville,\n", "                    'Pays': pays,\n", "                    'Type_Bien': 'Programme_Immobilier',\n", "                    'Nb_Proprietaires': nb_proprietaires,\n", "                    'Nb_Locataires': nb_locataires,\n", "                    'Total_Personnes': nb_proprietaires + nb_locataires\n", "                })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def get_fusion_proprietaires_biens_locataires(self):\n", "        \"\"\"Version simplifiee - affiche par bien/programme avec noms des locataires\"\"\"\n", "        results = []\n", "        \n", "        # Gestion Locative - par bien\n", "        if not self.df_biens.empty:\n", "            for _, bien_row in self.df_biens.iterrows():\n", "                bien_nom = bien_row['Bien']\n", "                type_bien = bien_row.get('Type', 'N/A')\n", "                \n", "                nb_proprietaires = len(self.df_proprietaires_locative[self.df_proprietaires_locative['Bien'] == bien_nom])\n", "                \n", "                locataires_bien = self.df_locataires_locative[self.df_locataires_locative['Bien'] == bien_nom]\n", "                nb_locataires = len(locataires_bien)\n", "                \n", "                # Obtenir les noms des locataires (utiliser les colonnes disponibles)\n", "                noms_locataires = []\n", "                for _, loc in locataires_bien.iterrows():\n", "                    # Construire le nom a partir des colonnes disponibles\n", "                    nom_parts = []\n", "                    for col in loc.index:\n", "                        if col not in ['Bien', 'Programme'] and pd.notna(loc[col]) and str(loc[col]).strip():\n", "                            nom_parts.append(str(loc[col]).strip())\n", "                    if nom_parts:\n", "                        noms_locataires.append(' '.join(nom_parts[:2]))  # Prendre les 2 premiers elements\n", "                \n", "                results.append({\n", "                    'Source': 'Gestion_Locative',\n", "                    'Bien_Programme': bien_nom,\n", "                    'Type_Bien': type_bien,\n", "                    'Nb_Proprietaires': nb_proprietaires,\n", "                    'Nb_Locataires': nb_locataires,\n", "                    'Noms_Locataires': '; '.join(noms_locataires) if noms_locataires else 'Aucun'\n", "                })\n", "        \n", "        # E-Syndic - par programme\n", "        if not self.df_programmes.empty:\n", "            for _, prog_row in self.df_programmes.iterrows():\n", "                programme_nom = prog_row['Programme']\n", "                ville = prog_row.get('Ville', 'N/A')\n", "                \n", "                nb_proprietaires = len(self.df_proprietaires_esyndic[self.df_proprietaires_esyndic['Programme'] == programme_nom])\n", "                \n", "                locataires_prog = self.df_locataires_esyndic[self.df_locataires_esyndic['Programme'] == programme_nom]\n", "                nb_locataires = len(locataires_prog)\n", "                \n", "                # Obtenir les noms des locataires\n", "                noms_locataires = []\n", "                for _, loc in locataires_prog.iterrows():\n", "                    nom_parts = []\n", "                    for col in loc.index:\n", "                        if col not in ['Bien', 'Programme'] and pd.notna(loc[col]) and str(loc[col]).strip():\n", "                            nom_parts.append(str(loc[col]).strip())\n", "                    if nom_parts:\n", "                        noms_locataires.append(' '.join(nom_parts[:2]))\n", "                \n", "                results.append({\n", "                    'Source': '<PERSON>_<PERSON>yn<PERSON>',\n", "                    'Bien_Programme': programme_nom,\n", "                    'Type_Bien': f'Programme_{ville}',\n", "                    'Nb_Proprietaires': nb_proprietaires,\n", "                    'Nb_Locataires': nb_locataires,\n", "                    'Noms_Locataires': '; '.join(noms_locataires) if noms_locataires else 'Aucun'\n", "                })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def get_nombre_evenements_par_programme(self):\n", "        \"\"\"Dataframe pour afficher le nombre d'evenements par programme\"\"\"\n", "        if self.df_programmes.empty:\n", "            return pd.DataFrame(columns=['Programme', 'Ville', 'Nb_Evenements'])\n", "        \n", "        results = []\n", "        for _, row in self.df_programmes.iterrows():\n", "            programme = row['Programme']\n", "            ville = row.get('Ville', 'N/A')\n", "            \n", "            nb_evenements = len(self.df_evenements[self.df_evenements['Programme'] == programme])\n", "            \n", "            results.append({\n", "                'Programme': programme,\n", "                'Ville': ville,\n", "                'Nb_Evenements': nb_evenements\n", "            })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def get_evenements_par_proprietaires_locataires(self):\n", "        \"\"\"Version simplifiee - compte les evenements par programme pour chaque personne\"\"\"\n", "        results = []\n", "        \n", "        # Compter les evenements par programme et les attribuer aux personnes\n", "        if not self.df_programmes.empty and not self.df_evenements.empty:\n", "            for _, prog_row in self.df_programmes.iterrows():\n", "                programme_nom = prog_row['Programme']\n", "                nb_evenements = len(self.df_evenements[self.df_evenements['Programme'] == programme_nom])\n", "                \n", "                if nb_evenements > 0:\n", "                    # Proprietaires de ce programme\n", "                    proprietaires_prog = self.df_proprietaires_esyndic[self.df_proprietaires_esyndic['Programme'] == programme_nom]\n", "                    for _, prop in proprietaires_prog.iterrows():\n", "                        nom_parts = []\n", "                        for col in prop.index:\n", "                            if col not in ['Bien', 'Programme'] and pd.notna(prop[col]) and str(prop[col]).strip():\n", "                                nom_parts.append(str(prop[col]).strip())\n", "                        nom_proprietaire = ' '.join(nom_parts[:2]) if nom_parts else 'Proprietaire_Inconnu'\n", "                        \n", "                        results.append({\n", "                            'Type_Personne': '<PERSON><PERSON><PERSON><PERSON>',\n", "                            'Nom_Personne': nom_proprietaire,\n", "                            'Programme': programme_nom,\n", "                            'Nb_Evenements': nb_evenements,\n", "                            'Source': '<PERSON>_Syn<PERSON>'\n", "                        })\n", "                    \n", "                    # Locataires de ce programme\n", "                    locataires_prog = self.df_locataires_esyndic[self.df_locataires_esyndic['Programme'] == programme_nom]\n", "                    for _, loc in locataires_prog.iterrows():\n", "                        nom_parts = []\n", "                        for col in loc.index:\n", "                            if col not in ['Bien', 'Programme'] and pd.notna(loc[col]) and str(loc[col]).strip():\n", "                                nom_parts.append(str(loc[col]).strip())\n", "                        nom_locataire = ' '.join(nom_parts[:2]) if nom_parts else 'Locataire_Inconnu'\n", "                        \n", "                        results.append({\n", "                            'Type_Personne': 'Locataire',\n", "                            'Nom_Personne': nom_locataire,\n", "                            'Programme': programme_nom,\n", "                            'Nb_Evenements': nb_evenements,\n", "                            'Source': '<PERSON>_Syn<PERSON>'\n", "                        })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def get_nombre_incidents_par_programme(self):\n", "        \"\"\"Dataframe pour afficher le nombre d'incidents par programme\"\"\"\n", "        if self.df_programmes.empty:\n", "            return pd.DataFrame(columns=['Programme', 'Ville', 'Nb_Incidents'])\n", "        \n", "        results = []\n", "        for _, row in self.df_programmes.iterrows():\n", "            programme = row['Programme']\n", "            ville = row.get('Ville', 'N/A')\n", "            \n", "            nb_incidents = len(self.df_incidents[self.df_incidents['Programme'] == programme])\n", "            \n", "            results.append({\n", "                'Programme': programme,\n", "                'Ville': ville,\n", "                'Nb_Incidents': nb_incidents\n", "            })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def generer_rapport_complet(self):\n", "        print(\"RAPPORT COMBINE E-SYNDIC + GESTION LOCATIVE\")\n", "        print(\"=\" * 50)\n", "        \n", "        # 1. Fusion dataframes programme et locataires par programme\n", "        print(\"\\n1. FUSION PROGRAMMES ET LOCATAIRES PAR PROGRAMME\")\n", "        df_fusion_prog = self.get_fusion_dataframes_programme_locataires()\n", "        if not df_fusion_prog.empty:\n", "            display(df_fusion_prog)\n", "            print(f\"Total proprietaires: {df_fusion_prog['Nb_Proprietaires'].sum()}\")\n", "            print(f\"Total locataires: {df_fusion_prog['Nb_Locataires'].sum()}\")\n", "        else:\n", "            print(\"Aucune donnee de programmes disponible\")\n", "        \n", "        # 2. Fusion proprietaires avec biens et noms des locataires\n", "        print(\"\\n2. BIENS/PROGRAMMES AVEC NOMS DES LOCATAIRES\")\n", "        df_fusion_prop = self.get_fusion_proprietaires_biens_locataires()\n", "        if not df_fusion_prop.empty:\n", "            display(df_fusion_prop)\n", "        else:\n", "            print(\"<PERSON><PERSON>ne donnee disponible\")\n", "        \n", "        # 3. Nombre d'evenements par programme\n", "        print(\"\\n3. NOMBRE D'EVENEMENTS PAR PROGRAMME\")\n", "        df_events_prog = self.get_nombre_evenements_par_programme()\n", "        if not df_events_prog.empty:\n", "            display(df_events_prog)\n", "            print(f\"Total evenements: {df_events_prog['Nb_Evenements'].sum()}\")\n", "        else:\n", "            print(\"Aucune donnee d'evenements disponible\")\n", "        \n", "        # 4. Evenements par proprietaires et locataires\n", "        print(\"\\n4. EVENEMENTS PAR PROPRIETAIRES ET LOCATAIRES\")\n", "        df_events_personnes = self.get_evenements_par_proprietaires_locataires()\n", "        if not df_events_personnes.empty:\n", "            display(df_events_personnes)\n", "            proprietaires_events = df_events_personnes[df_events_personnes['Type_Personne'] == 'Proprietaire']\n", "            locataires_events = df_events_personnes[df_events_personnes['Type_Personne'] == 'Locataire']\n", "            print(f\"Evenements organises par proprietaires: {proprietaires_events['Nb_Evenements'].sum()}\")\n", "            print(f\"Evenements organises par locataires: {locataires_events['Nb_Evenements'].sum()}\")\n", "        else:\n", "            print(\"Aucune donnee d'evenements par personnes disponible\")\n", "        \n", "        # 5. Nombre d'incidents par programme\n", "        print(\"\\n5. NOMBRE D'INCIDENTS PAR PROGRAMME\")\n", "        df_incidents_prog = self.get_nombre_incidents_par_programme()\n", "        if not df_incidents_prog.empty:\n", "            display(df_incidents_prog)\n", "            print(f\"Total incidents: {df_incidents_prog['Nb_Incidents'].sum()}\")\n", "        else:\n", "            print(\"Au<PERSON>ne donnee d'incidents disponible\")\n", "        \n", "        return {\n", "            'fusion_programmes': df_fusion_prog,\n", "            'fusion_proprietaires': df_fusion_prop,\n", "            'evenements_programmes': df_events_prog,\n", "            'evenements_personnes': df_events_personnes,\n", "            'incidents_programmes': df_incidents_prog\n", "        }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Utilisation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Creer l'instance de la classe\n", "metrics = CombinedMetrics(\n", "    df_biens, df_programmes, \n", "    df_proprietaires_locative, df_locataires_locative,\n", "    df_proprietaires_esyndic, df_locataires_esyndic,\n", "    df_incidents, df_evenements\n", ")\n", "\n", "# Generer le rapport complet\n", "resultats = metrics.generer_rapport_complet()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}