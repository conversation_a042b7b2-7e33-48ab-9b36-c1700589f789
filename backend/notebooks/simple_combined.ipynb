{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ANALYSE COMBINEE E-SYNDIC + GESTION LOCATIVE\n", "\n", "## Fonctionnalites implementees:\n", "\n", "1. **Fusion des dataframes programme et locataires** - Affiche pour chaque programme le nombre de proprietaires et locataires avec le type de bien\n", "2. **Fusion proprietaires avec biens et locataires** - Pour chaque proprietaire, affiche le nombre de biens en location et les noms des locataires\n", "3. **Nombre d'evenements par programme** - Dataframe montrant le nombre d'evenements par programme\n", "4. **Evenements par proprietaires et locataires** - Nombre d'evenements organises par proprietaires et par locataires\n", "5. **Nombre d'incidents par programme** - Dataframe montrant le nombre d'incidents par programme"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import ast"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fonctions de chargement"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def load_csv_direct(csv_file):\n", "    try:\n", "        df_raw = pd.read_csv(csv_file)\n", "        print(f\"Fichier charge: {csv_file}\")\n", "        print(f\"Nombre de lignes: {len(df_raw)}\")\n", "        return df_raw\n", "    except Exception as e:\n", "        print(f\"Erreur: {str(e)}\")\n", "        return None\n", "\n", "def parse_json_columns(df, json_columns):\n", "    df_copy = df.copy()\n", "    for col in json_columns:\n", "        if col in df_copy.columns:\n", "            df_copy[col] = df_copy[col].apply(lambda x: ast.literal_eval(x) if pd.notna(x) and x != '[]' and x != '{}' else [])\n", "    return df_copy\n", "\n", "def explode_column(df, col_name, parent_col='Parent'):\n", "    rows = []\n", "    for _, row in df.iterrows():\n", "        data_list = row.get(col_name)\n", "        if isinstance(data_list, list) and len(data_list) > 0:\n", "            for item in data_list:\n", "                if isinstance(item, dict):\n", "                    item_copy = item.copy()\n", "                    item_copy[parent_col] = row[parent_col]\n", "                    rows.append(item_copy)\n", "    return pd.DataFrame(rows)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chargement des donnees"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== CHARGEMENT GESTION LOCATIVE ===\n", "Fichier charge: G_locative.csv\n", "Nombre de lignes: 42\n", "Biens charges: 42\n", "Proprietaires: 34\n", "Locataires: 10\n"]}], "source": ["# Chargement Gestion Locative\n", "print(\"=== CHARGEMENT GESTION LOCATIVE ===\")\n", "df_biens_raw = load_csv_direct('G_locative.csv')\n", "\n", "if df_biens_raw is not None:\n", "    json_columns = ['proprietaires', 'locataires', 'charges', 'type', 'actifs']\n", "    df_biens = parse_json_columns(df_biens_raw, json_columns)\n", "    \n", "    df_biens.rename(columns={\n", "        'libelle': 'Bien',\n", "        'adresse': '<PERSON>ress<PERSON>',\n", "        'proprietaires': 'Proprietaires',\n", "        'locataires': 'Locataires',\n", "        'charges': 'Charges',\n", "        'totalCharges': 'TotalCharges',\n", "        'totaLoyer': 'TotalLoyer',\n", "        'totalImpayer': 'TotalImpayer',\n", "        'type': 'Type_Dict'\n", "    }, inplace=True)\n", "    \n", "    df_biens['Type'] = df_biens['Type_Dict'].apply(lambda x: x.get('libelle', 'N/A') if isinstance(x, dict) else 'N/A')\n", "    \n", "    df_proprietaires_locative = explode_column(df_biens, 'Proprietaires', 'Bien')\n", "    df_locataires_locative = explode_column(df_biens, 'Locataires', 'Bien')\n", "    \n", "    print(f\"Biens charges: {len(df_biens)}\")\n", "    print(f\"Proprietaires: {len(df_proprietaires_locative)}\")\n", "    print(f\"Locataires: {len(df_locataires_locative)}\")\n", "else:\n", "    df_biens = pd.DataFrame()\n", "    df_proprietaires_locative = pd.DataFrame()\n", "    df_locataires_locative = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== CHARGEMENT E-SYNDIC ===\n", "Fichier charge: E_syndic.csv\n", "Nombre de lignes: 1\n", "Programmes charges: 3\n", "Proprietaires: 164\n", "Locataires: 51\n", "Incidents: 61\n", "Evenements: 7\n"]}], "source": ["# Chargement E-Syndic\n", "print(\"\\n=== CHARGEMENT E-SYNDIC ===\")\n", "df_esyndic_raw = load_csv_direct('E_syndic.csv')\n", "\n", "if df_esyndic_raw is not None:\n", "    parsed_esyndic = ast.literal_eval(df_esyndic_raw['data'].iloc[0])\n", "    df_programmes = pd.DataFrame(parsed_esyndic)\n", "    \n", "    df_programmes.rename(columns={\n", "        'libelle': 'Programme',\n", "        'pays': 'Pays',\n", "        'ville': 'Ville',\n", "        'superficie': 'Superficie',\n", "        'proprietaire': 'Pro<PERSON>rietaire',\n", "        'locataires': 'Locataires',\n", "        'incidents': 'Incidents',\n", "        'evenements': 'Evenements'\n", "    }, inplace=True)\n", "    \n", "    df_proprietaires_esyndic = explode_column(df_programmes, 'Proprietaire', 'Programme')\n", "    df_locataires_esyndic = explode_column(df_programmes, 'Locataires', 'Programme')\n", "    df_incidents = explode_column(df_programmes, 'Incidents', 'Programme')\n", "    df_evenements = explode_column(df_programmes, 'Evenements', 'Programme')\n", "    \n", "    print(f\"Programmes charges: {len(df_programmes)}\")\n", "    print(f\"Proprietaires: {len(df_proprietaires_esyndic)}\")\n", "    print(f\"Locataires: {len(df_locataires_esyndic)}\")\n", "    print(f\"Incidents: {len(df_incidents)}\")\n", "    print(f\"Evenements: {len(df_evenements)}\")\n", "else:\n", "    df_programmes = pd.DataFrame()\n", "    df_proprietaires_esyndic = pd.DataFrame()\n", "    df_locataires_esyndic = pd.DataFrame()\n", "    df_incidents = pd.DataFrame()\n", "    df_evenements = pd.DataFrame()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Classe pour les metriques"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["class CombinedMetrics:\n", "    def __init__(self, df_biens, df_programmes, df_prop_loc, df_loc_loc, df_prop_syn, df_loc_syn, df_incidents, df_evenements):\n", "        self.df_biens = df_biens\n", "        self.df_programmes = df_programmes\n", "        self.df_proprietaires_locative = df_prop_loc\n", "        self.df_locataires_locative = df_loc_loc\n", "        self.df_proprietaires_esyndic = df_prop_syn\n", "        self.df_locataires_esyndic = df_loc_syn\n", "        self.df_incidents = df_incidents\n", "        self.df_evenements = df_evenements\n", "    \n", "    def get_fusion_dataframes_programme_locataires(self):\n", "        \"\"\"Fusion des dataframes programme et locataires avec type de bien par programme\"\"\"\n", "        results = []\n", "        \n", "        if not self.df_programmes.empty:\n", "            for _, prog_row in self.df_programmes.iterrows():\n", "                programme_nom = prog_row['Programme']\n", "                ville = prog_row.get('Ville', 'N/A')\n", "                pays = prog_row.get('Pays', 'N/A')\n", "                \n", "                nb_proprietaires = len(self.df_proprietaires_esyndic[self.df_proprietaires_esyndic['Programme'] == programme_nom])\n", "                nb_locataires = len(self.df_locataires_esyndic[self.df_locataires_esyndic['Programme'] == programme_nom])\n", "                \n", "                results.append({\n", "                    'Programme': programme_nom,\n", "                    'Ville': ville,\n", "                    'Pays': pays,\n", "                    'Type_Bien': 'Programme_Immobilier',\n", "                    'Nb_Proprietaires': nb_proprietaires,\n", "                    'Nb_Locataires': nb_locataires,\n", "                    'Total_Personnes': nb_proprietaires + nb_locataires\n", "                })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def get_fusion_proprietaires_biens_locataires(self):\n", "        \"\"\"Fusion pour afficher pour chaque proprietaire le nombre de biens en location et noms des locataires\"\"\"\n", "        results = []\n", "        \n", "        # Gestion Locative\n", "        if not self.df_proprietaires_locative.empty:\n", "            proprietaires_uniques = self.df_proprietaires_locative.groupby(['name', 'prenoms']).size().reset_index(name='count')\n", "            \n", "            for _, prop in proprietaires_uniques.iterrows():\n", "                proprietaire_nom = f\"{prop['name']} {prop['prenoms']}\".strip()\n", "                \n", "                biens_proprietaire = self.df_proprietaires_locative[\n", "                    (self.df_proprietaires_locative['name'] == prop['name']) & \n", "                    (self.df_proprietaires_locative['prenoms'] == prop['prenoms'])\n", "                ]\n", "                \n", "                nb_biens_total = len(biens_proprietaire)\n", "                nb_biens_loues = 0\n", "                noms_locataires = []\n", "                \n", "                for _, bien_prop in biens_proprietaire.iterrows():\n", "                    bien_nom = bien_prop['Bien']\n", "                    \n", "                    locataires_bien = self.df_locataires_locative[self.df_locataires_locative['Bien'] == bien_nom]\n", "                    if not locataires_bien.empty:\n", "                        nb_biens_loues += 1\n", "                        for _, loc in locataires_bien.iterrows():\n", "                            nom_locataire = f\"{loc.get('name', '')} {loc.get('prenoms', '')}\".strip()\n", "                            if nom_locataire:\n", "                                noms_locataires.append(f\"{nom_locataire} ({bien_nom})\")\n", "                \n", "                results.append({\n", "                    'Source': 'Gestion_Locative',\n", "                    'Proprietaire': proprietaire_nom,\n", "                    'Nb_Biens_Total': nb_biens_total,\n", "                    'Nb_B<PERSON>_Loues': nb_biens_loues,\n", "                    'Noms_Locataires_Biens': '; '.join(noms_locataires) if noms_locataires else 'Aucun'\n", "                })\n", "        \n", "        # E-<PERSON><PERSON><PERSON>\n", "        if not self.df_proprietaires_esyndic.empty:\n", "            proprietaires_uniques = self.df_proprietaires_esyndic.groupby(['name', 'prenoms']).size().reset_index(name='count')\n", "            \n", "            for _, prop in proprietaires_uniques.iterrows():\n", "                proprietaire_nom = f\"{prop['name']} {prop['prenoms']}\".strip()\n", "                \n", "                programmes_proprietaire = self.df_proprietaires_esyndic[\n", "                    (self.df_proprietaires_esyndic['name'] == prop['name']) & \n", "                    (self.df_proprietaires_esyndic['prenoms'] == prop['prenoms'])\n", "                ]\n", "                \n", "                nb_programmes_total = len(programmes_proprietaire)\n", "                nb_programmes_avec_locataires = 0\n", "                noms_locataires = []\n", "                \n", "                for _, prog_prop in programmes_proprietaire.iterrows():\n", "                    programme_nom = prog_prop['Programme']\n", "                    \n", "                    locataires_prog = self.df_locataires_esyndic[self.df_locataires_esyndic['Programme'] == programme_nom]\n", "                    if not locataires_prog.empty:\n", "                        nb_programmes_avec_locataires += 1\n", "                        for _, loc in locataires_prog.iterrows():\n", "                            nom_locataire = f\"{loc.get('name', '')} {loc.get('prenoms', '')}\".strip()\n", "                            if nom_locataire:\n", "                                noms_locataires.append(f\"{nom_locataire} ({programme_nom})\")\n", "                \n", "                results.append({\n", "                    'Source': '<PERSON>_<PERSON>yn<PERSON>',\n", "                    'Proprietaire': proprietaire_nom,\n", "                    'Nb_Biens_Total': nb_programmes_total,\n", "                    'Nb_Biens_Loues': nb_programmes_avec_locataires,\n", "                    'Noms_Locataires_Biens': '; '.join(noms_locataires) if noms_locataires else 'Aucun'\n", "                })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def get_nombre_evenements_par_programme(self):\n", "        \"\"\"Dataframe pour afficher le nombre d'evenements par programme\"\"\"\n", "        if self.df_programmes.empty:\n", "            return pd.DataFrame(columns=['Programme', 'Ville', 'Nb_Evenements'])\n", "        \n", "        results = []\n", "        for _, row in self.df_programmes.iterrows():\n", "            programme = row['Programme']\n", "            ville = row.get('Ville', 'N/A')\n", "            \n", "            nb_evenements = len(self.df_evenements[self.df_evenements['Programme'] == programme])\n", "            \n", "            results.append({\n", "                'Programme': programme,\n", "                'Ville': ville,\n", "                'Nb_Evenements': nb_evenements\n", "            })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def get_evenements_par_proprietaires_locataires(self):\n", "        \"\"\"Dataframe pour afficher le nombre d'evenements organises par proprietaires et locataires\"\"\"\n", "        results = []\n", "        \n", "        # Evenements par proprietaires (E-Syndic)\n", "        if not self.df_proprietaires_esyndic.empty and not self.df_evenements.empty:\n", "            proprietaires_uniques = self.df_proprietaires_esyndic.groupby(['name', 'prenoms']).size().reset_index(name='count')\n", "            \n", "            for _, prop in proprietaires_uniques.iterrows():\n", "                proprietaire_nom = f\"{prop['name']} {prop['prenoms']}\".strip()\n", "                \n", "                programmes_proprietaire = self.df_proprietaires_esyndic[\n", "                    (self.df_proprietaires_esyndic['name'] == prop['name']) & \n", "                    (self.df_proprietaires_esyndic['prenoms'] == prop['prenoms'])\n", "                ]['Programme'].unique()\n", "                \n", "                nb_evenements = 0\n", "                for programme in programmes_proprietaire:\n", "                    nb_evenements += len(self.df_evenements[self.df_evenements['Programme'] == programme])\n", "                \n", "                results.append({\n", "                    'Type_Personne': '<PERSON><PERSON><PERSON><PERSON>',\n", "                    'Nom_Personne': proprietaire_nom,\n", "                    'Nb_Evenements': nb_evenements,\n", "                    'Source': '<PERSON>_Syn<PERSON>'\n", "                })\n", "        \n", "        # Evenements par locataires (E-Syndic)\n", "        if not self.df_locataires_esyndic.empty and not self.df_evenements.empty:\n", "            locataires_uniques = self.df_locataires_esyndic.groupby(['name', 'prenoms']).size().reset_index(name='count')\n", "            \n", "            for _, loc in locataires_uniques.iterrows():\n", "                locataire_nom = f\"{loc['name']} {loc['prenoms']}\".strip()\n", "                \n", "                programmes_locataire = self.df_locataires_esyndic[\n", "                    (self.df_locataires_esyndic['name'] == loc['name']) & \n", "                    (self.df_locataires_esyndic['prenoms'] == loc['prenoms'])\n", "                ]['Programme'].unique()\n", "                \n", "                nb_evenements = 0\n", "                for programme in programmes_locataire:\n", "                    nb_evenements += len(self.df_evenements[self.df_evenements['Programme'] == programme])\n", "                \n", "                results.append({\n", "                    'Type_Personne': 'Locataire',\n", "                    'Nom_Personne': locataire_nom,\n", "                    'Nb_Evenements': nb_evenements,\n", "                    'Source': '<PERSON>_Syn<PERSON>'\n", "                })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def get_nombre_incidents_par_programme(self):\n", "        \"\"\"Dataframe pour afficher le nombre d'incidents par programme\"\"\"\n", "        if self.df_programmes.empty:\n", "            return pd.DataFrame(columns=['Programme', 'Ville', 'Nb_Incidents'])\n", "        \n", "        results = []\n", "        for _, row in self.df_programmes.iterrows():\n", "            programme = row['Programme']\n", "            ville = row.get('Ville', 'N/A')\n", "            \n", "            nb_incidents = len(self.df_incidents[self.df_incidents['Programme'] == programme])\n", "            \n", "            results.append({\n", "                'Programme': programme,\n", "                'Ville': ville,\n", "                'Nb_Incidents': nb_incidents\n", "            })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def generer_rapport_complet(self):\n", "        print(\"RAPPORT COMBINE E-SYNDIC + GESTION LOCATIVE\")\n", "        print(\"=\" * 50)\n", "        \n", "        # 1. Fusion dataframes programme et locataires par programme\n", "        print(\"\\n1. FUSION PROGRAMMES ET LOCATAIRES PAR PROGRAMME\")\n", "        df_fusion_prog = self.get_fusion_dataframes_programme_locataires()\n", "        if not df_fusion_prog.empty:\n", "            display(df_fusion_prog)\n", "            print(f\"Total proprietaires: {df_fusion_prog['Nb_Proprietaires'].sum()}\")\n", "            print(f\"Total locataires: {df_fusion_prog['Nb_Locataires'].sum()}\")\n", "        else:\n", "            print(\"Aucune donnee de programmes disponible\")\n", "        \n", "        # 2. Fusion proprietaires avec biens et noms des locataires\n", "        print(\"\\n2. PROPRIETAIRES AVEC BIENS ET NOMS DES LOCATAIRES\")\n", "        df_fusion_prop = self.get_fusion_proprietaires_biens_locataires()\n", "        if not df_fusion_prop.empty:\n", "            display(df_fusion_prop)\n", "        else:\n", "            print(\"Au<PERSON>ne donnee de proprietaires disponible\")\n", "        \n", "        # 3. Nombre d'evenements par programme\n", "        print(\"\\n3. NOMBRE D'EVENEMENTS PAR PROGRAMME\")\n", "        df_events_prog = self.get_nombre_evenements_par_programme()\n", "        if not df_events_prog.empty:\n", "            display(df_events_prog)\n", "            print(f\"Total evenements: {df_events_prog['Nb_Evenements'].sum()}\")\n", "        else:\n", "            print(\"Aucune donnee d'evenements disponible\")\n", "        \n", "        # 4. Evenements par proprietaires et locataires\n", "        print(\"\\n4. EVENEMENTS PAR PROPRIETAIRES ET LOCATAIRES\")\n", "        df_events_personnes = self.get_evenements_par_proprietaires_locataires()\n", "        if not df_events_personnes.empty:\n", "            display(df_events_personnes)\n", "            proprietaires_events = df_events_personnes[df_events_personnes['Type_Personne'] == 'Proprietaire']\n", "            locataires_events = df_events_personnes[df_events_personnes['Type_Personne'] == 'Locataire']\n", "            print(f\"Evenements organises par proprietaires: {proprietaires_events['Nb_Evenements'].sum()}\")\n", "            print(f\"Evenements organises par locataires: {locataires_events['Nb_Evenements'].sum()}\")\n", "        else:\n", "            print(\"Aucune donnee d'evenements par personnes disponible\")\n", "        \n", "        # 5. Nombre d'incidents par programme\n", "        print(\"\\n5. NOMBRE D'INCIDENTS PAR PROGRAMME\")\n", "        df_incidents_prog = self.get_nombre_incidents_par_programme()\n", "        if not df_incidents_prog.empty:\n", "            display(df_incidents_prog)\n", "            print(f\"Total incidents: {df_incidents_prog['Nb_Incidents'].sum()}\")\n", "        else:\n", "            print(\"Au<PERSON>ne donnee d'incidents disponible\")\n", "        \n", "        return {\n", "            'fusion_programmes': df_fusion_prog,\n", "            'fusion_proprietaires': df_fusion_prop,\n", "            'evenements_programmes': df_events_prog,\n", "            'evenements_personnes': df_events_personnes,\n", "            'incidents_programmes': df_incidents_prog\n", "        }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Utilisation"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RAPPORT COMBINE E-SYNDIC + GESTION LOCATIVE\n", "==================================================\n", "\n", "1. FUSION PROGRAMMES ET LOCATAIRES PAR PROGRAMME\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Programme</th>\n", "      <th>Ville</th>\n", "      <th>Pays</th>\n", "      <th>Type_Bien</th>\n", "      <th><PERSON><PERSON>_Proprietaires</th>\n", "      <th>Nb_Locataires</th>\n", "      <th>Total_Personnes</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CALLISTO ETOILE</td>\n", "      <td><PERSON>-<PERSON>am</td>\n", "      <td>Côte d'Ivoire</td>\n", "      <td>Programme_Immobilier</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CALLISTO BNETD PHASE 1</td>\n", "      <td><PERSON>-<PERSON>am</td>\n", "      <td>Côte d'Ivoire</td>\n", "      <td>Programme_Immobilier</td>\n", "      <td>74</td>\n", "      <td>0</td>\n", "      <td>74</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SYMPHONIA</td>\n", "      <td>Abidjan - Cocody</td>\n", "      <td>Côte d'Ivoire</td>\n", "      <td>Programme_Immobilier</td>\n", "      <td>90</td>\n", "      <td>51</td>\n", "      <td>141</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                Programme             Ville           Pays  \\\n", "0         CALLISTO ETOILE      Grand-Bassam  Côte d'Ivoire   \n", "1  CALLISTO BNETD PHASE 1      Grand-Bassam  Côte d'Ivoire   \n", "2               SYMPHONIA  Abidjan - Cocody  Côte d'Ivoire   \n", "\n", "              Type_Bien  Nb_Proprietaires  Nb_Locataires  Total_Personnes  \n", "0  Programme_Immobilier                 0              0                0  \n", "1  Programme_Immobilier                74              0               74  \n", "2  Programme_Immobilier                90             51              141  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Total proprietaires: 164\n", "Total locataires: 51\n", "\n", "2. PROPRIETAIRES AVEC BIENS ET NOMS DES LOCATAIRES\n"]}, {"ename": "KeyError", "evalue": "'name'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                  <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[6]\u001b[39m\u001b[32m, line 10\u001b[39m\n\u001b[32m      2\u001b[39m metrics = CombinedMetrics(\n\u001b[32m      3\u001b[39m     df_biens, df_programmes, \n\u001b[32m      4\u001b[39m     df_proprietaires_locative, df_locataires_locative,\n\u001b[32m      5\u001b[39m     df_proprietaires_esyndic, df_locataires_esyndic,\n\u001b[32m      6\u001b[39m     df_incidents, df_evenements\n\u001b[32m      7\u001b[39m )\n\u001b[32m      9\u001b[39m \u001b[38;5;66;03m# Generer le rapport complet\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m10\u001b[39m resultats = metrics.generer_rapport_complet()\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[5]\u001b[39m\u001b[32m, line 221\u001b[39m, in \u001b[36mCombinedMetrics.generer_rapport_complet\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    219\u001b[39m \u001b[38;5;66;03m# 2. Fusion proprietaires avec biens et noms des locataires\u001b[39;00m\n\u001b[32m    220\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m2. PROPRIETAIRES AVEC BIENS ET NOMS DES LOCATAIRES\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m--> \u001b[39m\u001b[32m221\u001b[39m df_fusion_prop = \u001b[38;5;28mself\u001b[39m.get_fusion_proprietaires_biens_locataires()\n\u001b[32m    222\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m df_fusion_prop.empty:\n\u001b[32m    223\u001b[39m     display(df_fusion_prop)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[5]\u001b[39m\u001b[32m, line 43\u001b[39m, in \u001b[36mCombinedMetrics.get_fusion_proprietaires_biens_locataires\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m     41\u001b[39m \u001b[38;5;66;03m# Gestion Locative\u001b[39;00m\n\u001b[32m     42\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m.df_proprietaires_locative.empty:\n\u001b[32m---> \u001b[39m\u001b[32m43\u001b[39m     proprietaires_uniques = \u001b[38;5;28mself\u001b[39m.df_proprietaires_locative.groupby([\u001b[33m'\u001b[39m\u001b[33mname\u001b[39m\u001b[33m'\u001b[39m, \u001b[33m'\u001b[39m\u001b[33mprenoms\u001b[39m\u001b[33m'\u001b[39m]).size().reset_index(name=\u001b[33m'\u001b[39m\u001b[33mcount\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m     45\u001b[39m     \u001b[38;5;28;01mfor\u001b[39;00m _, prop \u001b[38;5;129;01min\u001b[39;00m proprietaires_uniques.iterrows():\n\u001b[32m     46\u001b[39m         proprietaire_nom = \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mprop[\u001b[33m'\u001b[39m\u001b[33mname\u001b[39m\u001b[33m'\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mprop[\u001b[33m'\u001b[39m\u001b[33mprenoms\u001b[39m\u001b[33m'\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m.strip()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/KAH/lib/python3.13/site-packages/pandas/core/frame.py:9183\u001b[39m, in \u001b[36mDataFrame.groupby\u001b[39m\u001b[34m(self, by, axis, level, as_index, sort, group_keys, observed, dropna)\u001b[39m\n\u001b[32m   9180\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m level \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m by \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m   9181\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33mYou have to supply one of \u001b[39m\u001b[33m'\u001b[39m\u001b[33mby\u001b[39m\u001b[33m'\u001b[39m\u001b[33m and \u001b[39m\u001b[33m'\u001b[39m\u001b[33mlevel\u001b[39m\u001b[33m'\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m-> \u001b[39m\u001b[32m9183\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m DataFrameGroupBy(\n\u001b[32m   9184\u001b[39m     obj=\u001b[38;5;28mself\u001b[39m,\n\u001b[32m   9185\u001b[39m     keys=by,\n\u001b[32m   9186\u001b[39m     axis=axis,\n\u001b[32m   9187\u001b[39m     level=level,\n\u001b[32m   9188\u001b[39m     as_index=as_index,\n\u001b[32m   9189\u001b[39m     sort=sort,\n\u001b[32m   9190\u001b[39m     group_keys=group_keys,\n\u001b[32m   9191\u001b[39m     observed=observed,\n\u001b[32m   9192\u001b[39m     dropna=dropna,\n\u001b[32m   9193\u001b[39m )\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/KAH/lib/python3.13/site-packages/pandas/core/groupby/groupby.py:1329\u001b[39m, in \u001b[36mGroupBy.__init__\u001b[39m\u001b[34m(self, obj, keys, axis, level, grouper, exclusions, selection, as_index, sort, group_keys, observed, dropna)\u001b[39m\n\u001b[32m   1326\u001b[39m \u001b[38;5;28mself\u001b[39m.dropna = dropna\n\u001b[32m   1328\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m grouper \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1329\u001b[39m     grouper, exclusions, obj = get_grouper(\n\u001b[32m   1330\u001b[39m         obj,\n\u001b[32m   1331\u001b[39m         keys,\n\u001b[32m   1332\u001b[39m         axis=axis,\n\u001b[32m   1333\u001b[39m         level=level,\n\u001b[32m   1334\u001b[39m         sort=sort,\n\u001b[32m   1335\u001b[39m         observed=\u001b[38;5;28;01mFalse\u001b[39;00m \u001b[38;5;28;01mif\u001b[39;00m observed \u001b[38;5;129;01mis\u001b[39;00m lib.no_default \u001b[38;5;28;01melse\u001b[39;00m observed,\n\u001b[32m   1336\u001b[39m         dropna=\u001b[38;5;28mself\u001b[39m.dropna,\n\u001b[32m   1337\u001b[39m     )\n\u001b[32m   1339\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m observed \u001b[38;5;129;01mis\u001b[39;00m lib.no_default:\n\u001b[32m   1340\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28many\u001b[39m(ping._passed_categorical \u001b[38;5;28;01mfor\u001b[39;00m ping \u001b[38;5;129;01min\u001b[39;00m grouper.groupings):\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/KAH/lib/python3.13/site-packages/pandas/core/groupby/grouper.py:1043\u001b[39m, in \u001b[36mget_grouper\u001b[39m\u001b[34m(obj, key, axis, level, sort, observed, validate, dropna)\u001b[39m\n\u001b[32m   1041\u001b[39m         in_axis, level, gpr = \u001b[38;5;28;01mF<PERSON>e\u001b[39;00m, gpr, \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m   1042\u001b[39m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1043\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39;00m(gpr)\n\u001b[32m   1044\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(gpr, Grouper) \u001b[38;5;129;01mand\u001b[39;00m gpr.key \u001b[38;5;129;01<PERSON>\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m   1045\u001b[39m     \u001b[38;5;66;03m# Add key to exclusions\u001b[39;00m\n\u001b[32m   1046\u001b[39m     exclusions.add(gpr.key)\n", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m: 'name'"]}], "source": ["# Creer l'instance de la classe\n", "metrics = CombinedMetrics(\n", "    df_biens, df_programmes, \n", "    df_proprietaires_locative, df_locataires_locative,\n", "    df_proprietaires_esyndic, df_locataires_esyndic,\n", "    df_incidents, df_evenements\n", ")\n", "\n", "# Generer le rapport complet\n", "resultats = metrics.generer_rapport_complet()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}