{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ANALYSE COMBINEE E-SYNDIC + GESTION LOCATIVE\n", "\n", "## Nouvelles fonctionnalites ajoutees:\n", "\n", "1. **Fusion des dataframes programme et locataires** - Affiche pour chaque programme le nombre de proprietaires et locataires avec le type de bien\n", "2. **Fusion proprietaires avec biens et locataires** - Pour chaque proprietaire, affiche le nombre de biens en location et les noms des locataires\n", "3. **Nombre d'evenements par programme** - Dataframe montrant le nombre d'evenements par programme\n", "4. **Evenements par proprietaires et locataires** - Nombre d'evenements organises par proprietaires et par locataires\n", "5. **Nombre d'incidents par programme** - Dataframe montrant le nombre d'incidents par programme\n", "\n", "Toutes ces analyses sont integrees dans la classe `CombinedMetrics` et accessibles via le rapport complet."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import ast"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fonctions de chargement"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def load_csv_direct(csv_file):\n", "    try:\n", "        df_raw = pd.read_csv(csv_file)\n", "        print(f\"Fichier charge: {csv_file}\")\n", "        print(f\"Nombre de lignes: {len(df_raw)}\")\n", "        return df_raw\n", "    except Exception as e:\n", "        print(f\"Erreur: {str(e)}\")\n", "        return None\n", "\n", "def parse_json_columns(df, json_columns):\n", "    df_copy = df.copy()\n", "    for col in json_columns:\n", "        if col in df_copy.columns:\n", "            df_copy[col] = df_copy[col].apply(lambda x: ast.literal_eval(x) if pd.notna(x) and x != '[]' and x != '{}' else [])\n", "    return df_copy\n", "\n", "def explode_column(df, col_name, parent_col='Parent'):\n", "    rows = []\n", "    for _, row in df.iterrows():\n", "        data_list = row.get(col_name)\n", "        if isinstance(data_list, list) and len(data_list) > 0:\n", "            for item in data_list:\n", "                if isinstance(item, dict):\n", "                    item_copy = item.copy()\n", "                    item_copy[parent_col] = row[parent_col]\n", "                    rows.append(item_copy)\n", "    return pd.DataFrame(rows)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chargement Gestion Locative"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== CHARGEMENT GESTION LOCATIVE ===\n", "Fichier charge: G_locative.csv\n", "Nombre de lignes: 42\n", "Biens charges: 42\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Type</th>\n", "      <th>TotalLoyer</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>COMMERCE NADINE</td>\n", "      <td>SYMPHONIA</td>\n", "      <td>MAGASIN</td>\n", "      <td>700000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>VILLA NADINE</td>\n", "      <td>INOOVIM</td>\n", "      <td>Villa</td>\n", "      <td>600000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SYMPHONIA VILLA DUPLEX LOT 28 300 M2</td>\n", "      <td>RIVIERA EPHRATA</td>\n", "      <td>DUPLEX</td>\n", "      <td>1200000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                   Bien          Adresse     Type  TotalLoyer\n", "0                       COMMERCE NADINE        SYMPHONIA  MAGASIN      700000\n", "1                          VILLA NADINE          INOOVIM    Villa      600000\n", "2  SYMPHONIA VILLA DUPLEX LOT 28 300 M2  RIVIERA EPHRATA   DUPLEX     1200000"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Proprietaires: 34\n", "Locataires: 10\n", "Charges: 19\n"]}], "source": ["print(\"=== CHARGEMENT GESTION LOCATIVE ===\")\n", "df_biens_raw = load_csv_direct('G_locative.csv')\n", "\n", "if df_biens_raw is not None:\n", "    # Parser les colonnes JSON\n", "    json_columns = ['proprietaires', 'locataires', 'charges', 'type', 'actifs']\n", "    df_biens = parse_json_columns(df_biens_raw, json_columns)\n", "    \n", "    # Renommage\n", "    df_biens.rename(columns={\n", "        'libelle': 'Bien',\n", "        'adresse': '<PERSON>ress<PERSON>',\n", "        'proprietaires': 'Proprietaires',\n", "        'locataires': 'Locataires',\n", "        'charges': 'Charges',\n", "        'totalCharges': 'TotalCharges',\n", "        'totaLoyer': 'TotalLoyer',\n", "        'totalImpayer': 'TotalImpayer',\n", "        'type': 'Type_Dict'\n", "    }, inplace=True)\n", "    \n", "    # Extraire le type\n", "    df_biens['Type'] = df_biens['Type_Dict'].apply(lambda x: x.get('libelle', 'N/A') if isinstance(x, dict) else 'N/A')\n", "    \n", "    print(f\"Biens charges: {len(df_biens)}\")\n", "    display(df_biens[['<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>Loyer']].head(3))\n", "    \n", "    # DataFrames eclates\n", "    df_proprietaires_locative = explode_column(df_biens, 'Proprietaires', 'Bien')\n", "    df_locataires_locative = explode_column(df_biens, 'Locataires', 'Bien')\n", "    df_charges_locative = explode_column(df_biens, 'Charges', 'Bien')\n", "    \n", "    print(f\"Proprietaires: {len(df_proprietaires_locative)}\")\n", "    print(f\"Locataires: {len(df_locataires_locative)}\")\n", "    print(f\"Charges: {len(df_charges_locative)}\")\n", "else:\n", "    df_biens = pd.DataFrame()\n", "    df_proprietaires_locative = pd.DataFrame()\n", "    df_locataires_locative = pd.DataFrame()\n", "    df_charges_locative = pd.DataFrame()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chargement E-Syndic"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== CHARGEMENT E-SYNDIC ===\n", "Fichier charge: E_syndic.csv\n", "Nombre de lignes: 1\n", "Programmes charges: 3\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Programme</th>\n", "      <th>Ville</th>\n", "      <th>Pays</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CALLISTO ETOILE</td>\n", "      <td><PERSON>-<PERSON>am</td>\n", "      <td>Côte d'Ivoire</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CALLISTO BNETD PHASE 1</td>\n", "      <td><PERSON>-<PERSON>am</td>\n", "      <td>Côte d'Ivoire</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SYMPHONIA</td>\n", "      <td>Abidjan - Cocody</td>\n", "      <td>Côte d'Ivoire</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                Programme             Ville           Pays\n", "0         CALLISTO ETOILE      Grand-Bassam  Côte d'Ivoire\n", "1  CALLISTO BNETD PHASE 1      Grand-Bassam  Côte d'Ivoire\n", "2               SYMPHONIA  Abidjan - Cocody  Côte d'Ivoire"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Proprietaires: 164\n", "Locataires: 51\n", "Incidents: 61\n", "Evenements: 7\n"]}], "source": ["print(\"\\n=== CHARGEMENT E-SYNDIC ===\")\n", "df_esyndic_raw = load_csv_direct('E_syndic.csv')\n", "\n", "if df_esyndic_raw is not None:\n", "    # Parser la colonne data\n", "    parsed_esyndic = ast.literal_eval(df_esyndic_raw['data'].iloc[0])\n", "    df_programmes = pd.DataFrame(parsed_esyndic)\n", "    \n", "    # Renommage\n", "    df_programmes.rename(columns={\n", "        'libelle': 'Programme',\n", "        'pays': 'Pays',\n", "        'ville': 'Ville',\n", "        'superficie': 'Superficie',\n", "        'proprietaire': 'Pro<PERSON>rietaire',\n", "        'locataires': 'Locataires',\n", "        'incidents': 'Incidents',\n", "        'evenements': 'Evenements'\n", "    }, inplace=True)\n", "    \n", "    print(f\"Programmes charges: {len(df_programmes)}\")\n", "    display(df_programmes[['Programme', 'Ville', 'Pays']].head(3))\n", "    \n", "    # DataFrames eclates\n", "    df_proprietaires_esyndic = explode_column(df_programmes, 'Proprietaire', 'Programme')\n", "    df_locataires_esyndic = explode_column(df_programmes, 'Locataires', 'Programme')\n", "    df_incidents = explode_column(df_programmes, 'Incidents', 'Programme')\n", "    df_evenements = explode_column(df_programmes, 'Evenements', 'Programme')\n", "    \n", "    print(f\"Proprietaires: {len(df_proprietaires_esyndic)}\")\n", "    print(f\"Locataires: {len(df_locataires_esyndic)}\")\n", "    print(f\"Incidents: {len(df_incidents)}\")\n", "    print(f\"Evenements: {len(df_evenements)}\")\n", "else:\n", "    df_programmes = pd.DataFrame()\n", "    df_proprietaires_esyndic = pd.DataFrame()\n", "    df_locataires_esyndic = pd.DataFrame()\n", "    df_incidents = pd.DataFrame()\n", "    df_evenements = pd.DataFrame()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Classe simple pour metriques"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["class CombinedMetrics:\n", "    def __init__(self, df_biens, df_programmes, df_prop_loc, df_loc_loc, df_prop_syn, df_loc_syn, df_incidents, df_evenements):\n", "        self.df_biens = df_biens\n", "        self.df_programmes = df_programmes\n", "        self.df_proprietaires_locative = df_prop_loc\n", "        self.df_locataires_locative = df_loc_loc\n", "        self.df_proprietaires_esyndic = df_prop_syn\n", "        self.df_locataires_esyndic = df_loc_syn\n", "        self.df_incidents = df_incidents\n", "        self.df_evenements = df_evenements\n", "    \n", "    def get_total_proprietaires_locataires_combine(self):\n", "        results = []\n", "        \n", "        # Gestion Locative\n", "        if not self.df_biens.empty:\n", "            for _, row in self.df_biens.iterrows():\n", "                bien = row['Bien']\n", "                nb_prop = len(self.df_proprietaires_locative[self.df_proprietaires_locative['Bien'] == bien])\n", "                nb_loc = len(self.df_locataires_locative[self.df_locataires_locative['Bien'] == bien])\n", "                \n", "                results.append({\n", "                    'Source': 'Gestion_Locative',\n", "                    'Entite': bien,\n", "                    'Type': row.get('Type', 'N/A'),\n", "                    'Nb_Proprietaires': nb_prop,\n", "                    'Nb_Locataires': nb_loc,\n", "                    'Total_Personnes': nb_prop + nb_loc\n", "                })\n", "        \n", "        # E-<PERSON><PERSON><PERSON>\n", "        if not self.df_programmes.empty:\n", "            for _, row in self.df_programmes.iterrows():\n", "                programme = row['Programme']\n", "                nb_prop = len(self.df_proprietaires_esyndic[self.df_proprietaires_esyndic['Programme'] == programme])\n", "                nb_loc = len(self.df_locataires_esyndic[self.df_locataires_esyndic['Programme'] == programme])\n", "                \n", "                results.append({\n", "                    'Source': '<PERSON>_<PERSON>yn<PERSON>',\n", "                    'Entite': programme,\n", "                    'Type': 'Programme',\n", "                    'Nb_Proprietaires': nb_prop,\n", "                    'Nb_Locataires': nb_loc,\n", "                    'Total_Personnes': nb_prop + nb_loc\n", "                })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def get_nombre_biens_programmes_par_type(self):\n", "        results = []\n", "        \n", "        # Gestion Locative - par type de bien\n", "        if not self.df_biens.empty:\n", "            for type_bien in self.df_biens['Type'].unique():\n", "                biens_type = self.df_biens[self.df_biens['Type'] == type_bien]\n", "                nb_biens = len(biens_type)\n", "                \n", "                # Compter les biens loues\n", "                nb_loues = 0\n", "                for _, bien_row in biens_type.iterrows():\n", "                    bien = bien_row['Bien']\n", "                    nb_locataires = len(self.df_locataires_locative[self.df_locataires_locative['Bien'] == bien])\n", "                    if nb_locataires > 0:\n", "                        nb_loues += 1\n", "                \n", "                results.append({\n", "                    'Source': 'Gestion_Locative',\n", "                    'Type_Entite': type_bien,\n", "                    'Nb_Entites': nb_biens,\n", "                    'Nb_Occupees': nb_loues,\n", "                    'Nb_Libres': nb_biens - nb_loues,\n", "                    'Taux_Occupation': round((nb_loues / nb_biens * 100), 2) if nb_biens > 0 else 0\n", "                })\n", "        \n", "        # E-Syndic - programmes par ville\n", "        if not self.df_programmes.empty:\n", "            for ville in self.df_programmes['Ville'].unique():\n", "                programmes_ville = self.df_programmes[self.df_programmes['Ville'] == ville]\n", "                nb_programmes = len(programmes_ville)\n", "                \n", "                # Compter les programmes avec locataires\n", "                nb_avec_locataires = 0\n", "                for _, prog_row in programmes_ville.iterrows():\n", "                    programme = prog_row['Programme']\n", "                    nb_locataires = len(self.df_locataires_esyndic[self.df_locataires_esyndic['Programme'] == programme])\n", "                    if nb_locataires > 0:\n", "                        nb_avec_locataires += 1\n", "                \n", "                results.append({\n", "                    'Source': '<PERSON>_<PERSON>yn<PERSON>',\n", "                    'Type_Entite': f'Programmes_{ville}',\n", "                    'Nb_Entites': nb_programmes,\n", "                    'Nb_Occupees': nb_avec_locataires,\n", "                    'Nb_Libres': nb_programmes - nb_avec_locataires,\n", "                    'Taux_Occupation': round((nb_avec_locataires / nb_programmes * 100), 2) if nb_programmes > 0 else 0\n", "                })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def get_metriques_financieres_par_type(self):\n", "        if self.df_biens.empty:\n", "            return pd.DataFrame(columns=['Type_Bien', 'Nb_Biens', 'Total_Loyers', 'Total_Charges', 'Revenus_Nets', 'Total_Impayes', 'Taux_Impaye'])\n", "        \n", "        results = []\n", "        for type_bien in self.df_biens['Type'].unique():\n", "            biens_type = self.df_biens[self.df_biens['Type'] == type_bien]\n", "            \n", "            nb_biens = len(biens_type)\n", "            total_loyers = biens_type['TotalLoyer'].sum()\n", "            total_charges = biens_type['TotalCharges'].sum()\n", "            total_impayes = biens_type['TotalImpayer'].sum()\n", "            revenus_nets = total_loyers - total_charges\n", "            \n", "            results.append({\n", "                'Type_Bien': type_bien,\n", "                'Nb_Biens': nb_biens,\n", "                'Total_Loyers': total_loyers,\n", "                'Total_Charges': total_charges,\n", "                'Revenus_Nets': revenus_nets,\n", "                'Total_Impayes': total_impayes,\n", "                'Taux_Impaye': round((total_impayes / total_loyers * 100), 2) if total_loyers > 0 else 0\n", "            })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def get_incidents_evenements_par_programme(self):\n", "        if self.df_programmes.empty:\n", "            return pd.DataFrame(columns=['Programme', 'Ville', 'Nb_Incidents', 'Nb_Evenements', 'Total_Activite', 'Ratio_Incidents_Evenements'])\n", "        \n", "        results = []\n", "        for _, row in self.df_programmes.iterrows():\n", "            programme = row['Programme']\n", "            ville = row.get('Ville', 'N/A')\n", "            \n", "            nb_incidents = len(self.df_incidents[self.df_incidents['Programme'] == programme])\n", "            nb_evenements = len(self.df_evenements[self.df_evenements['Programme'] == programme])\n", "            \n", "            results.append({\n", "                'Programme': programme,\n", "                'Ville': ville,\n", "                'Nb_Incidents': nb_incidents,\n", "                'Nb_Evenements': nb_evenements,\n", "                'Total_Activite': nb_incidents + nb_evenements,\n", "                'Ratio_Incidents_Evenements': round((nb_incidents / nb_evenements), 2) if nb_evenements > 0 else 0\n", "            })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def get_biens_en_location_par_proprietaire_combine(self):\n", "        # Version simplifiee pour eviter les erreurs de colonnes\n", "        results = []\n", "        \n", "        # Gestion Locative - analyse simple par bien\n", "        if not self.df_biens.empty:\n", "            for _, bien_row in self.df_biens.iterrows():\n", "                bien_nom = bien_row['Bien']\n", "                type_bien = bien_row.get('Type', 'N/A')\n", "                \n", "                # Compter proprietaires et locataires pour ce bien\n", "                nb_proprietaires = len(self.df_proprietaires_locative[self.df_proprietaires_locative['Bien'] == bien_nom])\n", "                nb_locataires = len(self.df_locataires_locative[self.df_locataires_locative['Bien'] == bien_nom])\n", "                \n", "                statut = 'Loue' if nb_locataires > 0 else 'Libre'\n", "                \n", "                results.append({\n", "                    'Source': 'Gestion_Locative',\n", "                    'Bien_Programme': bien_nom,\n", "                    'Type': type_bien,\n", "                    'Nb_Proprietaires': nb_proprietaires,\n", "                    'Nb_Locataires': nb_locataires,\n", "                    'Statut': statut,\n", "                    'Revenus_Loyer': bien_row.get('TotalLoyer', 0),\n", "                    'Total_Charges': bien_row.get('TotalCharges', 0)\n", "                })\n", "        \n", "        # E-Syndic - analyse simple par programme\n", "        if not self.df_programmes.empty:\n", "            for _, prog_row in self.df_programmes.iterrows():\n", "                programme_nom = prog_row['Programme']\n", "                ville = prog_row.get('Ville', 'N/A')\n", "                \n", "                # Compter proprietaires et locataires pour ce programme\n", "                nb_proprietaires = len(self.df_proprietaires_esyndic[self.df_proprietaires_esyndic['Programme'] == programme_nom])\n", "                nb_locataires = len(self.df_locataires_esyndic[self.df_locataires_esyndic['Programme'] == programme_nom])\n", "                \n", "                statut = 'Occupe' if nb_locataires > 0 else 'Libre'\n", "                \n", "                results.append({\n", "                    'Source': '<PERSON>_<PERSON>yn<PERSON>',\n", "                    'Bien_Programme': programme_nom,\n", "                    'Type': f'Programme_{ville}',\n", "                    'Nb_Proprietaires': nb_proprietaires,\n", "                    'Nb_Locataires': nb_locataires,\n", "                    'Statut': statut,\n", "                    'Revenus_Loyer': 0,\n", "                    'Total_Charges': 0\n", "                })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def get_biens_en_location_par_proprietaire_combine_old(self):\n", "        results = []\n", "        \n", "        # Gestion Locative\n", "        if not self.df_proprietaires_locative.empty:\n", "            proprietaires_uniques = self.df_proprietaires_locative.groupby(['name', 'prenoms']).size().reset_index(name='count')\n", "            \n", "            for _, prop in proprietaires_uniques.iterrows():\n", "                proprietaire_nom = f\"{prop['name']} {prop['prenoms']}\".strip()\n", "                \n", "                # Trouver tous les biens de ce proprietaire\n", "                biens_proprietaire = self.df_proprietaires_locative[\n", "                    (self.df_proprietaires_locative['name'] == prop['name']) & \n", "                    (self.df_proprietaires_locative['prenoms'] == prop['prenoms'])\n", "                ]\n", "                \n", "                nb_biens_total = len(biens_proprietaire)\n", "                nb_biens_loues = 0\n", "                types_biens = set()\n", "                \n", "                for _, bien_prop in biens_proprietaire.iterrows():\n", "                    bien_nom = bien_prop['Bien']\n", "                    \n", "                    # Trouver le type du bien\n", "                    bien_info = self.df_biens[self.df_biens['Bien'] == bien_nom]\n", "                    if not bien_info.empty:\n", "                        type_bien = bien_info.iloc[0]['Type']\n", "                        types_biens.add(type_bien)\n", "                        \n", "                        # Verifier si le bien est loue\n", "                        nb_locataires = len(self.df_locataires_locative[self.df_locataires_locative['Bien'] == bien_nom])\n", "                        if nb_locataires > 0:\n", "                            nb_biens_loues += 1\n", "                \n", "                results.append({\n", "                    'Source': 'Gestion_Locative',\n", "                    'Proprietaire': proprietaire_nom,\n", "                    'Nb_Biens_Total': nb_biens_total,\n", "                    'Nb_B<PERSON>_Loues': nb_biens_loues,\n", "                    'Nb_Biens_Libres': nb_biens_total - nb_biens_loues,\n", "                    'Types_Biens': ', '.join(sorted(types_biens)),\n", "                    'Taux_Location': round((nb_biens_loues / nb_biens_total * 100), 2) if nb_biens_total > 0 else 0\n", "                })\n", "        \n", "        # E-<PERSON><PERSON><PERSON>\n", "        if not self.df_proprietaires_esyndic.empty:\n", "            proprietaires_uniques = self.df_proprietaires_esyndic.groupby(['name', 'prenoms']).size().reset_index(name='count')\n", "            \n", "            for _, prop in proprietaires_uniques.iterrows():\n", "                proprietaire_nom = f\"{prop['name']} {prop['prenoms']}\".strip()\n", "                \n", "                # Trouver tous les programmes de ce proprietaire\n", "                programmes_proprietaire = self.df_proprietaires_esyndic[\n", "                    (self.df_proprietaires_esyndic['name'] == prop['name']) & \n", "                    (self.df_proprietaires_esyndic['prenoms'] == prop['prenoms'])\n", "                ]\n", "                \n", "                nb_programmes_total = len(programmes_proprietaire)\n", "                nb_programmes_avec_locataires = 0\n", "                \n", "                for _, prog_prop in programmes_proprietaire.iterrows():\n", "                    programme_nom = prog_prop['Programme']\n", "                    \n", "                    # Verifier si le programme a des locataires\n", "                    nb_locataires = len(self.df_locataires_esyndic[self.df_locataires_esyndic['Programme'] == programme_nom])\n", "                    if nb_locataires > 0:\n", "                        nb_programmes_avec_locataires += 1\n", "                \n", "                results.append({\n", "                    'Source': '<PERSON>_<PERSON>yn<PERSON>',\n", "                    'Proprietaire': proprietaire_nom,\n", "                    'Nb_Biens_Total': nb_programmes_total,\n", "                    'Nb_Biens_Loues': nb_programmes_avec_locataires,\n", "                    'Nb_Biens_Libres': nb_programmes_total - nb_programmes_avec_locataires,\n", "                    'Types_Biens': 'Programme',\n", "                    'Taux_Location': round((nb_programmes_avec_locataires / nb_programmes_total * 100), 2) if nb_programmes_total > 0 else 0\n", "                })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def get_fusion_dataframes_programme_locataires(self):\n", "        \"\"\"Fusion des dataframes programme et locataires avec type de bien par programme\"\"\"\n", "        results = []\n", "        \n", "        # E-Syndic - Fusion programmes et locataires\n", "        if not self.df_programmes.empty:\n", "            for _, prog_row in self.df_programmes.iterrows():\n", "                programme_nom = prog_row['Programme']\n", "                ville = prog_row.get('Ville', 'N/A')\n", "                pays = prog_row.get('Pays', 'N/A')\n", "                \n", "                # Compter proprietaires et locataires\n", "                nb_proprietaires = len(self.df_proprietaires_esyndic[self.df_proprietaires_esyndic['Programme'] == programme_nom])\n", "                nb_locataires = len(self.df_locataires_esyndic[self.df_locataires_esyndic['Programme'] == programme_nom])\n", "                \n", "                results.append({\n", "                    'Programme': programme_nom,\n", "                    'Ville': ville,\n", "                    'Pays': pays,\n", "                    'Type_Bien': 'Programme_Immobilier',\n", "                    'Nb_Proprietaires': nb_proprietaires,\n", "                    'Nb_Locataires': nb_locataires,\n", "                    'Total_Personnes': nb_proprietaires + nb_locataires\n", "                })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def get_fusion_proprietaires_biens_locataires(self):\n", "        \"\"\"Fusion pour afficher pour chaque proprietaire le nombre de biens en location et noms des locataires\"\"\"\n", "        results = []\n", "        \n", "        # Gestion Locative\n", "        if not self.df_proprietaires_locative.empty:\n", "            proprietaires_uniques = self.df_proprietaires_locative.groupby(['name', 'prenoms']).size().reset_index(name='count')\n", "            \n", "            for _, prop in proprietaires_uniques.iterrows():\n", "                proprietaire_nom = f\"{prop['name']} {prop['prenoms']}\".strip()\n", "                \n", "                # Trouver tous les biens de ce proprietaire\n", "                biens_proprietaire = self.df_proprietaires_locative[\n", "                    (self.df_proprietaires_locative['name'] == prop['name']) & \n", "                    (self.df_proprietaires_locative['prenoms'] == prop['prenoms'])\n", "                ]\n", "                \n", "                nb_biens_total = len(biens_proprietaire)\n", "                nb_biens_loues = 0\n", "                noms_locataires = []\n", "                \n", "                for _, bien_prop in biens_proprietaire.iterrows():\n", "                    bien_nom = bien_prop['Bien']\n", "                    \n", "                    # Trouver les locataires de ce bien\n", "                    locataires_bien = self.df_locataires_locative[self.df_locataires_locative['Bien'] == bien_nom]\n", "                    if not locataires_bien.empty:\n", "                        nb_biens_loues += 1\n", "                        for _, loc in locataires_bien.iterrows():\n", "                            nom_locataire = f\"{loc.get('name', '')} {loc.get('prenoms', '')}\".strip()\n", "                            if nom_locataire:\n", "                                noms_locataires.append(f\"{nom_locataire} ({bien_nom})\")\n", "                \n", "                results.append({\n", "                    'Source': 'Gestion_Locative',\n", "                    'Proprietaire': proprietaire_nom,\n", "                    'Nb_Biens_Total': nb_biens_total,\n", "                    'Nb_B<PERSON>_Loues': nb_biens_loues,\n", "                    'Noms_Locataires_Biens': '; '.join(noms_locataires) if noms_locataires else 'Aucun'\n", "                })\n", "        \n", "        # E-<PERSON><PERSON><PERSON>\n", "        if not self.df_proprietaires_esyndic.empty:\n", "            proprietaires_uniques = self.df_proprietaires_esyndic.groupby(['name', 'prenoms']).size().reset_index(name='count')\n", "            \n", "            for _, prop in proprietaires_uniques.iterrows():\n", "                proprietaire_nom = f\"{prop['name']} {prop['prenoms']}\".strip()\n", "                \n", "                # Trouver tous les programmes de ce proprietaire\n", "                programmes_proprietaire = self.df_proprietaires_esyndic[\n", "                    (self.df_proprietaires_esyndic['name'] == prop['name']) & \n", "                    (self.df_proprietaires_esyndic['prenoms'] == prop['prenoms'])\n", "                ]\n", "                \n", "                nb_programmes_total = len(programmes_proprietaire)\n", "                nb_programmes_avec_locataires = 0\n", "                noms_locataires = []\n", "                \n", "                for _, prog_prop in programmes_proprietaire.iterrows():\n", "                    programme_nom = prog_prop['Programme']\n", "                    \n", "                    # Trouver les locataires de ce programme\n", "                    locataires_prog = self.df_locataires_esyndic[self.df_locataires_esyndic['Programme'] == programme_nom]\n", "                    if not locataires_prog.empty:\n", "                        nb_programmes_avec_locataires += 1\n", "                        for _, loc in locataires_prog.iterrows():\n", "                            nom_locataire = f\"{loc.get('name', '')} {loc.get('prenoms', '')}\".strip()\n", "                            if nom_locataire:\n", "                                noms_locataires.append(f\"{nom_locataire} ({programme_nom})\")\n", "                \n", "                results.append({\n", "                    'Source': '<PERSON>_<PERSON>yn<PERSON>',\n", "                    'Proprietaire': proprietaire_nom,\n", "                    'Nb_Biens_Total': nb_programmes_total,\n", "                    'Nb_Biens_Loues': nb_programmes_avec_locataires,\n", "                    'Noms_Locataires_Biens': '; '.join(noms_locataires) if noms_locataires else 'Aucun'\n", "                })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def get_nombre_evenements_par_programme(self):\n", "        \"\"\"Dataframe pour afficher le nombre d'evenements par programme\"\"\"\n", "        if self.df_programmes.empty:\n", "            return pd.DataFrame(columns=['Programme', 'Ville', 'Nb_Evenements'])\n", "        \n", "        results = []\n", "        for _, row in self.df_programmes.iterrows():\n", "            programme = row['Programme']\n", "            ville = row.get('Ville', 'N/A')\n", "            \n", "            nb_evenements = len(self.df_evenements[self.df_evenements['Programme'] == programme])\n", "            \n", "            results.append({\n", "                'Programme': programme,\n", "                'Ville': ville,\n", "                'Nb_Evenements': nb_evenements\n", "            })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def get_evenements_par_proprietaires_locataires(self):\n", "        \"\"\"Dataframe pour afficher le nombre d'evenements organises par proprietaires et locataires\"\"\"\n", "        results = []\n", "        \n", "        # Evenements par proprietaires (E-Syndic)\n", "        if not self.df_proprietaires_esyndic.empty and not self.df_evenements.empty:\n", "            proprietaires_uniques = self.df_proprietaires_esyndic.groupby(['name', 'prenoms']).size().reset_index(name='count')\n", "            \n", "            for _, prop in proprietaires_uniques.iterrows():\n", "                proprietaire_nom = f\"{prop['name']} {prop['prenoms']}\".strip()\n", "                \n", "                # Trouver les programmes de ce proprietaire\n", "                programmes_proprietaire = self.df_proprietaires_esyndic[\n", "                    (self.df_proprietaires_esyndic['name'] == prop['name']) & \n", "                    (self.df_proprietaires_esyndic['prenoms'] == prop['prenoms'])\n", "                ]['Programme'].unique()\n", "                \n", "                # Compter les evenements dans ces programmes\n", "                nb_evenements = 0\n", "                for programme in programmes_proprietaire:\n", "                    nb_evenements += len(self.df_evenements[self.df_evenements['Programme'] == programme])\n", "                \n", "                results.append({\n", "                    'Type_Personne': '<PERSON><PERSON><PERSON><PERSON>',\n", "                    'Nom_Personne': proprietaire_nom,\n", "                    'Nb_Evenements': nb_evenements,\n", "                    'Source': '<PERSON>_Syn<PERSON>'\n", "                })\n", "        \n", "        # Evenements par locataires (E-Syndic)\n", "        if not self.df_locataires_esyndic.empty and not self.df_evenements.empty:\n", "            locataires_uniques = self.df_locataires_esyndic.groupby(['name', 'prenoms']).size().reset_index(name='count')\n", "            \n", "            for _, loc in locataires_uniques.iterrows():\n", "                locataire_nom = f\"{loc['name']} {loc['prenoms']}\".strip()\n", "                \n", "                # Trouver les programmes de ce locataire\n", "                programmes_locataire = self.df_locataires_esyndic[\n", "                    (self.df_locataires_esyndic['name'] == loc['name']) & \n", "                    (self.df_locataires_esyndic['prenoms'] == loc['prenoms'])\n", "                ]['Programme'].unique()\n", "                \n", "                # Compter les evenements dans ces programmes\n", "                nb_evenements = 0\n", "                for programme in programmes_locataire:\n", "                    nb_evenements += len(self.df_evenements[self.df_evenements['Programme'] == programme])\n", "                \n", "                results.append({\n", "                    'Type_Personne': 'Locataire',\n", "                    'Nom_Personne': locataire_nom,\n", "                    'Nb_Evenements': nb_evenements,\n", "                    'Source': '<PERSON>_Syn<PERSON>'\n", "                })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def get_nombre_incidents_par_programme(self):\n", "        \"\"\"Dataframe pour afficher le nombre d'incidents par programme\"\"\"\n", "        if self.df_programmes.empty:\n", "            return pd.DataFrame(columns=['Programme', 'Ville', 'Nb_Incidents'])\n", "        \n", "        results = []\n", "        for _, row in self.df_programmes.iterrows():\n", "            programme = row['Programme']\n", "            ville = row.get('Ville', 'N/A')\n", "            \n", "            nb_incidents = len(self.df_incidents[self.df_incidents['Programme'] == programme])\n", "            \n", "            results.append({\n", "                'Programme': programme,\n", "                'Ville': ville,\n", "                'Nb_Incidents': nb_incidents\n", "            })\n", "        \n", "        return pd.DataFrame(results)\n", "    \n", "    def get_resume_global(self):\n", "        return {\n", "            'Total_Biens': len(self.df_biens),\n", "            'Total_Programmes': len(self.df_programmes),\n", "            'Total_Entites': len(self.df_biens) + len(self.df_programmes),\n", "            'Total_Proprietaires_Locative': len(self.df_proprietaires_locative),\n", "            'Total_Locataires_Locative': len(self.df_locataires_locative),\n", "            'Total_Proprietaires_ESyndic': len(self.df_proprietaires_esyndic),\n", "            'Total_Locataires_ESyndic': len(self.df_locataires_esyndic),\n", "            'Total_Proprietaires_Global': len(self.df_proprietaires_locative) + len(self.df_proprietaires_esyndic),\n", "            'Total_Locataires_Global': len(self.df_locataires_locative) + len(self.df_locataires_esyndic),\n", "            'Total_Incidents': len(self.df_incidents),\n", "            'Total_Evenements': len(self.df_evenements)\n", "        }\n", "    \n", "    def generer_rapport_complet(self):\n", "        print(\"RAPPORT COMBINE E-SYNDIC + GESTION LOCATIVE\")\n", "        print(\"=\" * 50)\n", "        \n", "        # 1. Fusion dataframes programme et locataires par programme\n", "        print(\"\\n1. FUSION PROGRAMMES ET LOCATAIRES PAR PROGRAMME\")\n", "        df_fusion_prog = self.get_fusion_dataframes_programme_locataires()\n", "        if not df_fusion_prog.empty:\n", "            display(df_fusion_prog)\n", "            print(f\"Total proprietaires: {df_fusion_prog['Nb_Proprietaires'].sum()}\")\n", "            print(f\"Total locataires: {df_fusion_prog['Nb_Locataires'].sum()}\")\n", "        else:\n", "            print(\"Aucune donnee de programmes disponible\")\n", "        \n", "        # 2. Fusion proprietaires avec biens et noms des locataires\n", "        print(\"\\n2. PROPRIETAIRES AVEC BIENS ET NOMS DES LOCATAIRES\")\n", "        df_fusion_prop = self.get_fusion_proprietaires_biens_locataires()\n", "        if not df_fusion_prop.empty:\n", "            display(df_fusion_prop)\n", "        else:\n", "            print(\"Au<PERSON>ne donnee de proprietaires disponible\")\n", "        \n", "        # 3. Nombre d'evenements par programme\n", "        print(\"\\n3. NOMBRE D'EVENEMENTS PAR PROGRAMME\")\n", "        df_events_prog = self.get_nombre_evenements_par_programme()\n", "        if not df_events_prog.empty:\n", "            display(df_events_prog)\n", "            print(f\"Total evenements: {df_events_prog['Nb_Evenements'].sum()}\")\n", "        else:\n", "            print(\"Aucune donnee d'evenements disponible\")\n", "        \n", "        # 4. Evenements par proprietaires et locataires\n", "        print(\"\\n4. EVENEMENTS PAR PROPRIETAIRES ET LOCATAIRES\")\n", "        df_events_personnes = self.get_evenements_par_proprietaires_locataires()\n", "        if not df_events_personnes.empty:\n", "            display(df_events_personnes)\n", "            # Separer par type de personne\n", "            proprietaires_events = df_events_personnes[df_events_personnes['Type_Personne'] == 'Proprietaire']\n", "            locataires_events = df_events_personnes[df_events_personnes['Type_Personne'] == 'Locataire']\n", "            print(f\"Evenements organises par proprietaires: {proprietaires_events['Nb_Evenements'].sum()}\")\n", "            print(f\"Evenements organises par locataires: {locataires_events['Nb_Evenements'].sum()}\")\n", "        else:\n", "            print(\"Aucune donnee d'evenements par personnes disponible\")\n", "        \n", "        # 5. Nombre d'incidents par programme\n", "        print(\"\\n5. NOMBRE D'INCIDENTS PAR PROGRAMME\")\n", "        df_incidents_prog = self.get_nombre_incidents_par_programme()\n", "        if not df_incidents_prog.empty:\n", "            display(df_incidents_prog)\n", "            print(f\"Total incidents: {df_incidents_prog['Nb_Incidents'].sum()}\")\n", "        else:\n", "            print(\"Au<PERSON>ne donnee d'incidents disponible\")\n", "        \n", "        # 6. Proprietaires et locataires combines (ancien)\n", "        print(\"\\n6. PROPRIETAIRES ET LOCATAIRES COMBINES (DETAIL)\")\n", "        df_prop_loc = self.get_total_proprietaires_locataires_combine()\n", "        display(df_prop_loc)\n", "        \n", "        # 7. Metriques financieres (Gestion Locative)\n", "        print(\"\\n7. METRIQUES FINANCIERES PAR TYPE (GESTION LOCATIVE)\")\n", "        df_financier = self.get_metriques_financieres_par_type()\n", "        if not df_financier.empty:\n", "            display(df_financier)\n", "        else:\n", "            print(\"Aucune donnee financiere disponible\")\n", "        \n", "        # 8. Resume global\n", "        print(\"\\n8. RESUME GLOBAL\")\n", "        resume_data = self.get_resume_global()\n", "        for key, value in resume_data.items():\n", "            print(f\"  - {key.replace('_', ' ')}: {value}\")\n", "        \n", "        return {\n", "            'fusion_programmes': df_fusion_prog,\n", "            'fusion_proprietaires': df_fusion_prop,\n", "            'evenements_programmes': df_events_prog,\n", "            'evenements_personnes': df_events_personnes,\n", "            'incidents_programmes': df_incidents_prog,\n", "            'proprietaires_locataires': df_prop_loc,\n", "            'metriques_financieres': df_financier,\n", "            'resume_global': resume_data\n", "        }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Utilisation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test des nouvelles fonctionnalites\n", "print(\"=== TEST DES NOUVELLES FONCTIONNALITES ===\")\n", "\n", "# Creer l'instance de la classe\n", "metrics = CombinedMetrics(\n", "    df_biens, df_programmes, \n", "    df_proprietaires_locative, df_locataires_locative,\n", "    df_proprietaires_esyndic, df_locataires_esyndic,\n", "    df_incidents, df_evenements\n", ")\n", "\n", "# 1. Fusion programmes et locataires\n", "print(\"\\n1. FUSION PROGRAMMES ET LOCATAIRES:\")\n", "df_fusion_prog = metrics.get_fusion_dataframes_programme_locataires()\n", "display(df_fusion_prog)\n", "\n", "# 2. Proprietaires avec noms des locataires\n", "print(\"\\n2. PROPRIETAIRES AVEC NOMS DES LOCATAIRES:\")\n", "df_prop_locataires = metrics.get_fusion_proprietaires_biens_locataires()\n", "display(df_prop_locataires)\n", "\n", "# 3. Evenements par programme\n", "print(\"\\n3. EVENEMENTS PAR PROGRAMME:\")\n", "df_events_prog = metrics.get_nombre_evenements_par_programme()\n", "display(df_events_prog)\n", "\n", "# 4. Evenements par proprietaires et locataires\n", "print(\"\\n4. EVENEMENTS PAR PROPRIETAIRES ET LOCATAIRES:\")\n", "df_events_personnes = metrics.get_evenements_par_proprietaires_locataires()\n", "display(df_events_personnes)\n", "\n", "# 5. Incidents par programme\n", "print(\"\\n5. INCIDENTS PAR PROGRAMME:\")\n", "df_incidents_prog = metrics.get_nombre_incidents_par_programme()\n", "display(df_incidents_prog)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Rapport complet avec nouvelles fonctionnalites"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generer le rapport complet avec toutes les nouvelles fonctionnalites\n", "print(\"=== RAPPORT COMPLET AVEC NOUVELLES FONCTIONNALITES ===\")\n", "\n", "# Creer l'instance de la classe\n", "metrics = CombinedMetrics(\n", "    df_biens, df_programmes, \n", "    df_proprietaires_locative, df_locataires_locative,\n", "    df_proprietaires_esyndic, df_locataires_esyndic,\n", "    df_incidents, df_evenements\n", ")\n", "\n", "# Generer le rapport complet\n", "resultats = metrics.generer_rapport_complet()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RAPPORT COMBINE E-SYNDIC + GESTION LOCATIVE\n", "==================================================\n", "\n", "1. PROPRIETAIRES ET LOCATAIRES COMBINES\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Source</th>\n", "      <th>Entite</th>\n", "      <th>Type</th>\n", "      <th><PERSON><PERSON>_Proprietaires</th>\n", "      <th>Nb_Locataires</th>\n", "      <th>Total_Personnes</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>COMMERCE NADINE</td>\n", "      <td>MAGASIN</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>VILLA NADINE</td>\n", "      <td>Villa</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA VILLA DUPLEX LOT 28 300 M2</td>\n", "      <td>DUPLEX</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA VILLA DUPLEX LOT 25</td>\n", "      <td>DUPLEX</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>VILLA DUPLEX CITE DU PORT</td>\n", "      <td>DUPLEX</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA VIILA DUPLEX LOT 49</td>\n", "      <td>DUPLEX</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA RIVIERA EPHRATA LOT 104</td>\n", "      <td>DUPLEX</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA RIVIERA EPHRATA LOT 75</td>\n", "      <td>DUPLEX</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA RIVIERA EPHRATA LOT 51</td>\n", "      <td>BLOC D'IMMEUBLE</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>LAURIERS 20</td>\n", "      <td>Villa</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA LOT 78</td>\n", "      <td>BLOC D'IMMEUBLE</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIUM BLOC E</td>\n", "      <td>BLOC D'IMMEUBLE</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA VILLA 45</td>\n", "      <td>DUPLEX</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIUM BLOC D</td>\n", "      <td>BLOC D'IMMEUBLE</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIUM BLOC C</td>\n", "      <td>BLOC D'IMMEUBLE</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIUM BLOC B</td>\n", "      <td>BLOC D'IMMEUBLE</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIUM BLOC A</td>\n", "      <td>BLOC D'IMMEUBLE</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA LOT 14</td>\n", "      <td>DUPLEX</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>LES CONCESSIONS DE BINGERVILLES</td>\n", "      <td>CONCESSION</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA DUPLEX LOT 94</td>\n", "      <td>DUPLEX</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA DUPLEX LOT 83 ILOT 09</td>\n", "      <td>DUPLEX</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA VILLA DUPLEX LOT 48</td>\n", "      <td>DUPLEX</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA DUPLEX LOT 21</td>\n", "      <td>DUPLEX</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA DUPLEX LOT 85</td>\n", "      <td>DUPLEX</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA DUPLEX LOT 107</td>\n", "      <td>DUPLEX</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA DUPLEX LOT 37</td>\n", "      <td>DUPLEX</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA IMMEUBLE LOT 101</td>\n", "      <td>Imme<PERSON>le</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>LAURIERS 15 LOT 275</td>\n", "      <td>Imme<PERSON>le</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA IMMEUBLE LOT 68</td>\n", "      <td>Imme<PERSON>le</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA IMMEUBLE LOT 59</td>\n", "      <td>Imme<PERSON>le</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA  IMMEUBLE LOT 68</td>\n", "      <td>Imme<PERSON>le</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA VILLA DUPLEX 5 LOT 64</td>\n", "      <td>DUPLEX</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA VILLA DUPLEX  LOT 86</td>\n", "      <td>DUPLEX</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA VILLA DUPLEX  LOT 97</td>\n", "      <td>DUPLEX</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA VILLA DUPLEX 2 LOT 106</td>\n", "      <td>DUPLEX</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>M'BADON VILLA</td>\n", "      <td>Villa</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA VILLA LOT 79</td>\n", "      <td>Villa</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>IMMEUBLE CISSE PORT BOUET</td>\n", "      <td>Imme<PERSON>le</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>IMMEUBLE CISSE JULES VERNE</td>\n", "      <td>Imme<PERSON>le</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>L'IMMEUBLE DE BIETRY SCI KAD</td>\n", "      <td>Imme<PERSON>le</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>IMMEUBLE SCI KAD REMBLAIS</td>\n", "      <td>Imme<PERSON>le</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>EGDGDG</td>\n", "      <td>Imme<PERSON>le</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>E_Syndic</td>\n", "      <td>CALLISTO ETOILE</td>\n", "      <td>Programme</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>E_Syndic</td>\n", "      <td>CALLISTO BNETD PHASE 1</td>\n", "      <td>Programme</td>\n", "      <td>74</td>\n", "      <td>0</td>\n", "      <td>74</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>E_Syndic</td>\n", "      <td>SYMPHONIA</td>\n", "      <td>Programme</td>\n", "      <td>90</td>\n", "      <td>51</td>\n", "      <td>141</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              Source                                Entite             Type  \\\n", "0   Gestion_Locative                       COMMERCE NADINE          MAGASIN   \n", "1   Gestion_Locative                          VILLA NADINE            Villa   \n", "2   Gestion_Locative  SYMPHONIA VILLA DUPLEX LOT 28 300 M2           DUPLEX   \n", "3   Gestion_Locative         SYMPHONIA VILLA DUPLEX LOT 25           DUPLEX   \n", "4   Gestion_Locative             VILLA DUPLEX CITE DU PORT           DUPLEX   \n", "5   Gestion_Locative         SYMPHONIA VIILA DUPLEX LOT 49           DUPLEX   \n", "6   Gestion_Locative     SYMPHONIA RIVIERA EPHRATA LOT 104           DUPLEX   \n", "7   Gestion_Locative      SYMPHONIA RIVIERA EPHRATA LOT 75           DUPLEX   \n", "8   Gestion_Locative      SYMPHONIA RIVIERA EPHRATA LOT 51  BLOC D'IMMEUBLE   \n", "9   Gestion_Locative                           LAURIERS 20            Villa   \n", "10  Gestion_Locative                      SYMPHONIA LOT 78  BLOC D'IMMEUBLE   \n", "11  Gestion_Locative                     SYMPH<PERSON>IUM BLOC E  BLOC D'IMMEUBLE   \n", "12  Gestion_Locative                    SYMPHONIA VILLA 45           DUPLEX   \n", "13  Gestion_Locative                     SYMPH<PERSON>IUM BLOC D  BLOC D'IMMEUBLE   \n", "14  Gestion_Locative                     SYMPHONIUM BLOC C  BLOC D'IMMEUBLE   \n", "15  Gestion_Locative                     SYMPHONIUM BLOC B  BLOC D'IMMEUBLE   \n", "16  Gestion_Locative                     SYMPH<PERSON>IUM BLOC A  BLOC D'IMMEUBLE   \n", "17  Gestion_Locative                      SYMPHONIA LOT 14           DUPLEX   \n", "18  Gestion_Locative       LES CONCESSIONS DE BINGERVILLES       CONCESSION   \n", "19  Gestion_Locative               SYMPHONIA DUPLEX LOT 94           DUPLEX   \n", "20  Gestion_Locative       SYMPHONIA DUPLEX LOT 83 ILOT 09           DUPLEX   \n", "21  Gestion_Locative         SYMPHONIA VILLA DUPLEX LOT 48           DUPLEX   \n", "22  Gestion_Locative               SYMPHONIA DUPLEX LOT 21           DUPLEX   \n", "23  Gestion_Locative               SYMPHONIA DUPLEX LOT 85           DUPLEX   \n", "24  Gestion_Locative              SYMPHONIA DUPLEX LOT 107           DUPLEX   \n", "25  Gestion_Locative               SYMPHONIA DUPLEX LOT 37           DUPLEX   \n", "26  Gestion_Locative            SYMPHONIA IMMEUBLE LOT 101         Immeuble   \n", "27  Gestion_Locative                   LAURIERS 15 LOT 275         Immeuble   \n", "28  Gestion_Locative             SYMPHONIA IMMEUBLE LOT 68         Immeuble   \n", "29  Gestion_Locative             SYMPHONIA IMMEUBLE LOT 59         Immeuble   \n", "30  Gestion_Locative            SYMPHONIA  IMMEUBLE LOT 68         Immeuble   \n", "31  Gestion_Locative       SYMPHONIA VILLA DUPLEX 5 LOT 64           DUPLEX   \n", "32  Gestion_Locative        SYMPHONIA VILLA DUPLEX  LOT 86           DUPLEX   \n", "33  Gestion_Locative        SYMPHONIA VILLA DUPLEX  LOT 97           DUPLEX   \n", "34  Gestion_Locative      SYMPHONIA VILLA DUPLEX 2 LOT 106           DUPLEX   \n", "35  Gestion_Locative                         M'BADON VILLA            Villa   \n", "36  Gestion_Locative                SYMPHONIA VILLA LOT 79            Villa   \n", "37  Gestion_Locative             IMMEUBLE CISSE PORT BOUET         Immeuble   \n", "38  Gestion_Locative            IMMEUBLE CISSE JULES VERNE         Immeuble   \n", "39  Gestion_Locative          L'IMMEUBLE DE BIETRY SCI KAD         Immeuble   \n", "40  Gestion_Locative             IMMEUBLE SCI KAD REMBLAIS         Immeuble   \n", "41  Gestion_Locative                                EGDGDG         Immeuble   \n", "42          E_Syndic                       CALLISTO ETOILE        Programme   \n", "43          E_Syndic                CALLISTO BNETD PHASE 1        Programme   \n", "44          E_Syndic                             SYMPHONIA        Programme   \n", "\n", "    Nb_Proprietaires  Nb_Locataires  Total_Personnes  \n", "0                  1              0                1  \n", "1                  1              0                1  \n", "2                  0              0                0  \n", "3                  0              0                0  \n", "4                  0              0                0  \n", "5                  1              0                1  \n", "6                  1              0                1  \n", "7                  1              0                1  \n", "8                  1              0                1  \n", "9                  1              0                1  \n", "10                 1              0                1  \n", "11                 1              0                1  \n", "12                 0              0                0  \n", "13                 1              0                1  \n", "14                 1              0                1  \n", "15                 1              0                1  \n", "16                 1              0                1  \n", "17                 1              1                2  \n", "18                 0              0                0  \n", "19                 1              2                3  \n", "20                 1              1                2  \n", "21                 1              1                2  \n", "22                 1              1                2  \n", "23                 1              0                1  \n", "24                 1              0                1  \n", "25                 0              0                0  \n", "26                 1              0                1  \n", "27                 1              0                1  \n", "28                 1              0                1  \n", "29                 1              1                2  \n", "30                 1              2                3  \n", "31                 1              0                1  \n", "32                 1              1                2  \n", "33                 1              0                1  \n", "34                 1              0                1  \n", "35                 1              0                1  \n", "36                 0              0                0  \n", "37                 1              0                1  \n", "38                 1              0                1  \n", "39                 1              0                1  \n", "40                 1              0                1  \n", "41                 0              0                0  \n", "42                 0              0                0  \n", "43                74              0               74  \n", "44                90             51              141  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "2. BIENS ET PROGRAMMES PAR TYPE\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Source</th>\n", "      <th>Type_Entite</th>\n", "      <th>Nb_Entites</th>\n", "      <th>Nb_Occupees</th>\n", "      <th>Nb_Libres</th>\n", "      <th>Taux_Occupation</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>MAGASIN</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>Villa</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>DUPLEX</td>\n", "      <td>19</td>\n", "      <td>6</td>\n", "      <td>13</td>\n", "      <td>31.58</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>BLOC D'IMMEUBLE</td>\n", "      <td>7</td>\n", "      <td>0</td>\n", "      <td>7</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>CONCESSION</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>Imme<PERSON>le</td>\n", "      <td>10</td>\n", "      <td>2</td>\n", "      <td>8</td>\n", "      <td>20.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>E_Syndic</td>\n", "      <td>Programmes_Grand-Bassam</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>E_Syndic</td>\n", "      <td>Programmes_Abidjan - Cocody</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>100.00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             Source                  Type_Entite  Nb_Entites  Nb_Occupees  \\\n", "0  Gestion_Locative                      MAGASIN           1            0   \n", "1  Gestion_Locative                        Villa           4            0   \n", "2  Gestion_Locative                       DUPLEX          19            6   \n", "3  Gestion_Locative              BLOC D'IMMEUBLE           7            0   \n", "4  Gestion_Locative                   CONCESSION           1            0   \n", "5  Gestion_Locative                     Immeuble          10            2   \n", "6          E_Syndic      Programmes_Grand-Bassam           2            0   \n", "7          E_Syndic  Programmes_Abidjan - Cocody           1            1   \n", "\n", "   Nb_Libres  Taux_Occupation  \n", "0          1             0.00  \n", "1          4             0.00  \n", "2         13            31.58  \n", "3          7             0.00  \n", "4          1             0.00  \n", "5          8            20.00  \n", "6          2             0.00  \n", "7          0           100.00  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "3. BIENS EN LOCATION PAR PROPRIETAIRE (COMBINE)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Source</th>\n", "      <th>Bien_Programme</th>\n", "      <th>Type</th>\n", "      <th><PERSON><PERSON>_Proprietaires</th>\n", "      <th>Nb_Locataires</th>\n", "      <th>Statut</th>\n", "      <th><PERSON><PERSON><PERSON>_<PERSON></th>\n", "      <th>Total_Charges</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>COMMERCE NADINE</td>\n", "      <td>MAGASIN</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>700000</td>\n", "      <td>30000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>VILLA NADINE</td>\n", "      <td>Villa</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>600000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA VILLA DUPLEX LOT 28 300 M2</td>\n", "      <td>DUPLEX</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>1200000</td>\n", "      <td>45000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA VILLA DUPLEX LOT 25</td>\n", "      <td>DUPLEX</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>1150000</td>\n", "      <td>45000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>VILLA DUPLEX CITE DU PORT</td>\n", "      <td>DUPLEX</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>1800000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA VIILA DUPLEX LOT 49</td>\n", "      <td>DUPLEX</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>1205000</td>\n", "      <td>45000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA RIVIERA EPHRATA LOT 104</td>\n", "      <td>DUPLEX</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>355000</td>\n", "      <td>45000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA RIVIERA EPHRATA LOT 75</td>\n", "      <td>DUPLEX</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>705000</td>\n", "      <td>45000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA RIVIERA EPHRATA LOT 51</td>\n", "      <td>BLOC D'IMMEUBLE</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>600000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>LAURIERS 20</td>\n", "      <td>Villa</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>250000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA LOT 78</td>\n", "      <td>BLOC D'IMMEUBLE</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>1250150</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIUM BLOC E</td>\n", "      <td>BLOC D'IMMEUBLE</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>2200000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA VILLA 45</td>\n", "      <td>DUPLEX</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>1000000</td>\n", "      <td>45000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIUM BLOC D</td>\n", "      <td>BLOC D'IMMEUBLE</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>4000000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIUM BLOC C</td>\n", "      <td>BLOC D'IMMEUBLE</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>4300000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIUM BLOC B</td>\n", "      <td>BLOC D'IMMEUBLE</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>4000000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIUM BLOC A</td>\n", "      <td>BLOC D'IMMEUBLE</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>4500000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA LOT 14</td>\n", "      <td>DUPLEX</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>1100000</td>\n", "      <td>45000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>LES CONCESSIONS DE BINGERVILLES</td>\n", "      <td>CONCESSION</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>4800000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA DUPLEX LOT 94</td>\n", "      <td>DUPLEX</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>1000000</td>\n", "      <td>45000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA DUPLEX LOT 83 ILOT 09</td>\n", "      <td>DUPLEX</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>955000</td>\n", "      <td>45000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA VILLA DUPLEX LOT 48</td>\n", "      <td>DUPLEX</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>1000000</td>\n", "      <td>45000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA DUPLEX LOT 21</td>\n", "      <td>DUPLEX</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>900000</td>\n", "      <td>45000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA DUPLEX LOT 85</td>\n", "      <td>DUPLEX</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>1600000</td>\n", "      <td>45000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA DUPLEX LOT 107</td>\n", "      <td>DUPLEX</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>600000</td>\n", "      <td>45000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA DUPLEX LOT 37</td>\n", "      <td>DUPLEX</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>900000</td>\n", "      <td>45000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA IMMEUBLE LOT 101</td>\n", "      <td>Imme<PERSON>le</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>1500000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>LAURIERS 15 LOT 275</td>\n", "      <td>Imme<PERSON>le</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>600000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA IMMEUBLE LOT 68</td>\n", "      <td>Imme<PERSON>le</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>1150000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA IMMEUBLE LOT 59</td>\n", "      <td>Imme<PERSON>le</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>1400000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA  IMMEUBLE LOT 68</td>\n", "      <td>Imme<PERSON>le</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>1850000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA VILLA DUPLEX 5 LOT 64</td>\n", "      <td>DUPLEX</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>1500000</td>\n", "      <td>80000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA VILLA DUPLEX  LOT 86</td>\n", "      <td>DUPLEX</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>950000</td>\n", "      <td>45000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA VILLA DUPLEX  LOT 97</td>\n", "      <td>DUPLEX</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>800000</td>\n", "      <td>45000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA VILLA DUPLEX 2 LOT 106</td>\n", "      <td>DUPLEX</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>1355000</td>\n", "      <td>45000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>M'BADON VILLA</td>\n", "      <td>Villa</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>2850000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>SYMPHONIA VILLA LOT 79</td>\n", "      <td>Villa</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>IMMEUBLE CISSE PORT BOUET</td>\n", "      <td>Imme<PERSON>le</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>2305000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>IMMEUBLE CISSE JULES VERNE</td>\n", "      <td>Imme<PERSON>le</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>1630000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>L'IMMEUBLE DE BIETRY SCI KAD</td>\n", "      <td>Imme<PERSON>le</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>4050000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>IMMEUBLE SCI KAD REMBLAIS</td>\n", "      <td>Imme<PERSON>le</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>2355000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>Gestion_Locative</td>\n", "      <td>EGDGDG</td>\n", "      <td>Imme<PERSON>le</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>E_Syndic</td>\n", "      <td>CALLISTO ETOILE</td>\n", "      <td>Programme_Grand-<PERSON>am</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>E_Syndic</td>\n", "      <td>CALLISTO BNETD PHASE 1</td>\n", "      <td>Programme_Grand-<PERSON>am</td>\n", "      <td>74</td>\n", "      <td>0</td>\n", "      <td>Libre</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>E_Syndic</td>\n", "      <td>SYMPHONIA</td>\n", "      <td>Programme_Abidjan - Cocody</td>\n", "      <td>90</td>\n", "      <td>51</td>\n", "      <td>Occupe</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              Source                        Bien_Programme  \\\n", "0   Gestion_Locative                       COMMERCE NADINE   \n", "1   Gestion_Locative                          VILLA NADINE   \n", "2   Gestion_Locative  SYMPHONIA VILLA DUPLEX LOT 28 300 M2   \n", "3   Gestion_Locative         SYMPHONIA VILLA DUPLEX LOT 25   \n", "4   Gestion_Locative             VILLA DUPLEX CITE DU PORT   \n", "5   Gestion_Locative         SYMPHONIA VIILA DUPLEX LOT 49   \n", "6   Gestion_Locative     SYMPHONIA RIVIERA EPHRATA LOT 104   \n", "7   Gestion_Locative      SYMPHONIA RIVIERA EPHRATA LOT 75   \n", "8   Gestion_Locative      SYMPHONIA RIVIERA EPHRATA LOT 51   \n", "9   Gestion_Locative                           LAURIERS 20   \n", "10  Gestion_Locative                      SYMPHONIA LOT 78   \n", "11  Gestion_Locative                     SYMPHONIUM BLOC E   \n", "12  Gestion_Locative                    SYMPHONIA VILLA 45   \n", "13  Gestion_Locative                     SYMPHONIUM BLOC D   \n", "14  Gestion_Locative                     SYMPHONIUM BLOC C   \n", "15  Gestion_Locative                     SYMPHONIUM BLOC B   \n", "16  Gestion_Locative                     SYMPHONIUM BLOC A   \n", "17  Gestion_Locative                      SYMPHONIA LOT 14   \n", "18  Gestion_Locative       LES CONCESSIONS DE BINGERVILLES   \n", "19  Gestion_Locative               SYMPHONIA DUPLEX LOT 94   \n", "20  Gestion_Locative       SYMPHONIA DUPLEX LOT 83 ILOT 09   \n", "21  Gestion_Locative         SYMPHONIA VILLA DUPLEX LOT 48   \n", "22  Gestion_Locative               SYMPHONIA DUPLEX LOT 21   \n", "23  Gestion_Locative               SYMPHONIA DUPLEX LOT 85   \n", "24  Gestion_Locative              SYMPHONIA DUPLEX LOT 107   \n", "25  Gestion_Locative               SYMPHONIA DUPLEX LOT 37   \n", "26  Gestion_Locative            SYMPHONIA IMMEUBLE LOT 101   \n", "27  Gestion_Locative                   LAURIERS 15 LOT 275   \n", "28  Gestion_Locative             SYMPHONIA IMMEUBLE LOT 68   \n", "29  Gestion_Locative             SYMPHONIA IMMEUBLE LOT 59   \n", "30  Gestion_Locative            SYMPHONIA  IMMEUBLE LOT 68   \n", "31  Gestion_Locative       SYMPHONIA VILLA DUPLEX 5 LOT 64   \n", "32  Gestion_Locative        SYMPHONIA VILLA DUPLEX  LOT 86   \n", "33  Gestion_Locative        SYMPHONIA VILLA DUPLEX  LOT 97   \n", "34  Gestion_Locative      SYMPHONIA VILLA DUPLEX 2 LOT 106   \n", "35  Gestion_Locative                         M'BADON VILLA   \n", "36  Gestion_Locative                SYMPHONIA VILLA LOT 79   \n", "37  Gestion_Locative             IMMEUBLE CISSE PORT BOUET   \n", "38  Gestion_Locative            IMMEUBLE CISSE JULES VERNE   \n", "39  Gestion_Locative          L'IMMEUBLE DE BIETRY SCI KAD   \n", "40  Gestion_Locative             IMMEUBLE SCI KAD REMBLAIS   \n", "41  Gestion_Locative                                EGDGDG   \n", "42          E_Syndic                       CALLISTO ETOILE   \n", "43          E_Syndic                CALLISTO BNETD PHASE 1   \n", "44          E_Syndic                             SYMPHONIA   \n", "\n", "                          Type  Nb_Proprietaires  Nb_Locataires  Statut  \\\n", "0                      MAGASIN                 1              0   Libre   \n", "1                        Villa                 1              0   Libre   \n", "2                       DUPLEX                 0              0   Libre   \n", "3                       DUPLEX                 0              0   Libre   \n", "4                       DUPLEX                 0              0   Libre   \n", "5                       DUPLEX                 1              0   Libre   \n", "6                       DUPLEX                 1              0   Libre   \n", "7                       DUPLEX                 1              0   Libre   \n", "8              BLOC D'IMMEUBLE                 1              0   Libre   \n", "9                        Villa                 1              0   Libre   \n", "10             BLOC D'IMMEUBLE                 1              0   Libre   \n", "11             BLOC D'IMMEUBLE                 1              0   Libre   \n", "12                      DUPLEX                 0              0   Libre   \n", "13             BLOC D'IMMEUBLE                 1              0   Libre   \n", "14             BLOC D'IMMEUBLE                 1              0   Libre   \n", "15             BLOC D'IMMEUBLE                 1              0   Libre   \n", "16             BLOC D'IMMEUBLE                 1              0   Libre   \n", "17                      DUPLEX                 1              1    Loue   \n", "18                  CONCESSION                 0              0   Libre   \n", "19                      DUPLEX                 1              2    Loue   \n", "20                      DUPLEX                 1              1    Loue   \n", "21                      DUPLEX                 1              1    Loue   \n", "22                      DUPLEX                 1              1    Loue   \n", "23                      DUPLEX                 1              0   Libre   \n", "24                      DUPLEX                 1              0   Libre   \n", "25                      DUPLEX                 0              0   Libre   \n", "26                    Immeuble                 1              0   Libre   \n", "27                    Immeuble                 1              0   Libre   \n", "28                    Immeuble                 1              0   Libre   \n", "29                    Immeuble                 1              1    Loue   \n", "30                    Immeuble                 1              2    Loue   \n", "31                      DUPLEX                 1              0   Libre   \n", "32                      DUPLEX                 1              1    Loue   \n", "33                      DUPLEX                 1              0   Libre   \n", "34                      DUPLEX                 1              0   Libre   \n", "35                       Villa                 1              0   Libre   \n", "36                       Villa                 0              0   Libre   \n", "37                    Immeuble                 1              0   Libre   \n", "38                    Immeuble                 1              0   Libre   \n", "39                    Immeuble                 1              0   Libre   \n", "40                    Immeuble                 1              0   Libre   \n", "41                    Immeuble                 0              0   Libre   \n", "42      Programme_Grand-Bassam                 0              0   Libre   \n", "43      Programme_Grand-Bassam                74              0   Libre   \n", "44  Programme_Abidjan - Cocody                90             51  Occupe   \n", "\n", "    Revenus_Loyer  Total_Charges  \n", "0          700000          30000  \n", "1          600000              0  \n", "2         1200000          45000  \n", "3         1150000          45000  \n", "4         1800000              0  \n", "5         1205000          45000  \n", "6          355000          45000  \n", "7          705000          45000  \n", "8          600000              0  \n", "9          250000              0  \n", "10        1250150              0  \n", "11        2200000              0  \n", "12        1000000          45000  \n", "13        4000000              0  \n", "14        4300000              0  \n", "15        4000000              0  \n", "16        4500000              0  \n", "17        1100000          45000  \n", "18        4800000              0  \n", "19        1000000          45000  \n", "20         955000          45000  \n", "21        1000000          45000  \n", "22         900000          45000  \n", "23        1600000          45000  \n", "24         600000          45000  \n", "25         900000          45000  \n", "26        1500000              0  \n", "27         600000              0  \n", "28        1150000              0  \n", "29        1400000              0  \n", "30        1850000              0  \n", "31        1500000          80000  \n", "32         950000          45000  \n", "33         800000          45000  \n", "34        1355000          45000  \n", "35        2850000              0  \n", "36              0              0  \n", "37        2305000              0  \n", "38        1630000              0  \n", "39        4050000              0  \n", "40        2355000              0  \n", "41              0              0  \n", "42              0              0  \n", "43              0              0  \n", "44              0              0  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "4. METRIQUES FINANCIERES PAR TYPE (GESTION LOCATIVE)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Type_Bien</th>\n", "      <th>Nb_Biens</th>\n", "      <th>Total_Loyers</th>\n", "      <th>Total_Charges</th>\n", "      <th>Revenus_Nets</th>\n", "      <th>Total_Impayes</th>\n", "      <th><PERSON><PERSON>_<PERSON><PERSON><PERSON>e</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>MAGASIN</td>\n", "      <td>1</td>\n", "      <td>700000</td>\n", "      <td>30000</td>\n", "      <td>670000</td>\n", "      <td>730000</td>\n", "      <td>104.29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Villa</td>\n", "      <td>4</td>\n", "      <td>3700000</td>\n", "      <td>0</td>\n", "      <td>3700000</td>\n", "      <td>14800000</td>\n", "      <td>400.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>DUPLEX</td>\n", "      <td>19</td>\n", "      <td>20075000</td>\n", "      <td>845000</td>\n", "      <td>19230000</td>\n", "      <td>149425000</td>\n", "      <td>744.33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>BLOC D'IMMEUBLE</td>\n", "      <td>7</td>\n", "      <td>20850150</td>\n", "      <td>0</td>\n", "      <td>20850150</td>\n", "      <td>219100750</td>\n", "      <td>1050.84</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>CONCESSION</td>\n", "      <td>1</td>\n", "      <td>4800000</td>\n", "      <td>0</td>\n", "      <td>4800000</td>\n", "      <td>38400000</td>\n", "      <td>800.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Imme<PERSON>le</td>\n", "      <td>10</td>\n", "      <td>16840000</td>\n", "      <td>0</td>\n", "      <td>16840000</td>\n", "      <td>80165006</td>\n", "      <td>476.04</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         Type_Bien  Nb_Biens  Total_Loyers  Total_Charges  Revenus_Nets  \\\n", "0          MAGASIN         1        700000          30000        670000   \n", "1            Villa         4       3700000              0       3700000   \n", "2           DUPLEX        19      20075000         845000      19230000   \n", "3  BLOC D'IMMEUBLE         7      20850150              0      20850150   \n", "4       CONCESSION         1       4800000              0       4800000   \n", "5         Immeuble        10      16840000              0      16840000   \n", "\n", "   Total_Impayes  Tau<PERSON>_<PERSON>mpaye  \n", "0         730000       104.29  \n", "1       14800000       400.00  \n", "2      149425000       744.33  \n", "3      219100750      1050.84  \n", "4       38400000       800.00  \n", "5       80165006       476.04  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "5. INCIDENTS ET EVENEMENTS PAR PROGRAMME (E-SYNDIC)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Programme</th>\n", "      <th>Ville</th>\n", "      <th>Nb_Incidents</th>\n", "      <th>Nb_Evenements</th>\n", "      <th>Total_Activite</th>\n", "      <th>Ratio_Incidents_Evenements</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CALLISTO ETOILE</td>\n", "      <td><PERSON>-<PERSON>am</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CALLISTO BNETD PHASE 1</td>\n", "      <td><PERSON>-<PERSON>am</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SYMPHONIA</td>\n", "      <td>Abidjan - Cocody</td>\n", "      <td>61</td>\n", "      <td>7</td>\n", "      <td>68</td>\n", "      <td>8.71</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                Programme             Ville  Nb_Incidents  Nb_Evenements  \\\n", "0         CALLISTO ETOILE      Grand-Bassam             0              0   \n", "1  CALLISTO BNETD PHASE 1      Grand-Bassam             0              0   \n", "2               SYMPHONIA  Abidjan - Cocody            61              7   \n", "\n", "   Total_Activite  Ratio_Incidents_Evenements  \n", "0               0                        0.00  \n", "1               0                        0.00  \n", "2              68                        8.71  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "6. RESUME GLOBAL\n", "  - Total Biens: 42\n", "  - Total Programmes: 3\n", "  - Total Entites: 45\n", "  - Total Proprietaires Locative: 34\n", "  - Total Locataires Locative: 10\n", "  - Total Proprietaires ESyndic: 164\n", "  - Total Locataires ESyndic: 51\n", "  - Total Proprietaires Global: 198\n", "  - Total Locataires Global: 61\n", "  - Total Incidents: 61\n", "  - Total Evenements: 7\n"]}], "source": ["# Creation de l'instance\n", "metrics = CombinedMetrics(\n", "    df_biens, df_programmes,\n", "    df_proprietaires_locative, df_locataires_locative,\n", "    df_proprietaires_esyndic, df_locataires_esyndic,\n", "    df_incidents, df_evenements\n", ")\n", "\n", "# Generation du rapport complet\n", "rapport_combine = metrics.generer_rapport_complet()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Utilisation individuelle des metriques"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "EXEMPLES D'UTILISATION INDIVIDUELLE\n", "========================================\n", "\n", "1. Proprietaires et locataires combines:\n", "Total entites analysees: 45\n", "Total personnes (proprietaires + locataires): 259\n", "\n", "2. Metriques financieres:\n", "Revenus nets totaux: 66,090,150 XOF\n", "Type le plus rentable: BLOC D'IMMEUBLE (20,850,150 XOF)\n", "\n", "3. Incidents et evenements:\n", "Total incidents: 61\n", "Total evenements: 7\n", "Ratio incidents/evenements global: 8.71\n", "\n", "4. Biens et programmes par statut:\n", "Total entites analysees: 45\n", "Repartition par statut:\n", "  - Libre: 36\n", "  - Loue: 8\n", "  - Occupe: 1\n", "<PERSON><PERSON><PERSON> to<PERSON> (Gestion Locative): 66,965,150 XOF\n"]}], "source": ["# Exemples d'utilisation individuelle des metriques\n", "print(\"\\nEXEMPLES D'UTILISATION INDIVIDUELLE\")\n", "print(\"=\" * 40)\n", "\n", "# 1. Proprietaires et locataires combines\n", "print(\"\\n1. Proprietaires et locataires combines:\")\n", "df_prop_loc_individual = metrics.get_total_proprietaires_locataires_combine()\n", "print(f\"Total entites analysees: {len(df_prop_loc_individual)}\")\n", "if not df_prop_loc_individual.empty:\n", "    total_personnes = df_prop_loc_individual['Total_Personnes'].sum()\n", "    print(f\"Total personnes (proprietaires + locataires): {total_personnes}\")\n", "\n", "# 2. Metriques financieres\n", "print(\"\\n2. Metriques financieres:\")\n", "df_financier_individual = metrics.get_metriques_financieres_par_type()\n", "if not df_financier_individual.empty:\n", "    revenus_totaux = df_financier_individual['Revenus_Nets'].sum()\n", "    print(f\"Revenus nets totaux: {revenus_totaux:,.0f} XOF\")\n", "    \n", "    type_plus_rentable = df_financier_individual.loc[df_financier_individual['Revenus_Nets'].idxmax()]\n", "    print(f\"Type le plus rentable: {type_plus_rentable['Type_Bien']} ({type_plus_rentable['Revenus_Nets']:,.0f} XOF)\")\n", "else:\n", "    print(\"Aucune donnee financiere disponible\")\n", "\n", "# 3. Incidents et evenements\n", "print(\"\\n3. Incidents et evenements:\")\n", "df_incidents_individual = metrics.get_incidents_evenements_par_programme()\n", "if not df_incidents_individual.empty:\n", "    total_incidents = df_incidents_individual['Nb_Incidents'].sum()\n", "    total_evenements = df_incidents_individual['Nb_Evenements'].sum()\n", "    print(f\"Total incidents: {total_incidents}\")\n", "    print(f\"Total evenements: {total_evenements}\")\n", "    \n", "    if total_evenements > 0:\n", "        ratio_global = total_incidents / total_evenements\n", "        print(f\"Ratio incidents/evenements global: {ratio_global:.2f}\")\n", "else:\n", "    print(\"Aucune donnee d'incidents/evenements disponible\")\n", "\n", "# 4. Biens et programmes par statut\n", "print(\"\\n4. Biens et programmes par statut:\")\n", "df_biens_prog = metrics.get_biens_en_location_par_proprietaire_combine()\n", "if not df_biens_prog.empty:\n", "    print(f\"Total entites analysees: {len(df_biens_prog)}\")\n", "    \n", "    # Repartition par statut\n", "    repartition_statut = df_biens_prog['Statut'].value_counts()\n", "    print(\"Repartition par statut:\")\n", "    for statut, count in repartition_statut.items():\n", "        print(f\"  - {statut}: {count}\")\n", "    \n", "    # Revenus totaux (Gestion Locative seulement)\n", "    revenus_totaux = df_biens_prog[df_biens_prog['Source'] == 'Gestion_Locative']['Revenus_Loyer'].sum()\n", "    if revenus_totaux > 0:\n", "        print(f\"Revenus totaux (Gestion Locative): {revenus_totaux:,.0f} XOF\")\n", "else:\n", "    print(\"<PERSON><PERSON>ne donnee disponible\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 4}